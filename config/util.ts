import { ClassConstructor, plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';

export interface ServiceToServiceConfig {
  baseUrl: string;
  auth0: { aud: string };
}

export interface ServiceToServiceConfigWithApiKey extends ServiceToServiceConfig {
  apiKey: string;
}

export interface Auth0Config {
  clientId: string;
  clientSecret: string;
  auth0Url: string;
}

export interface MulesoftServiceConfig {
  baseUrl: string;
  clientId: string;
  clientSecret: string;
}

export function parseAndValidateEnvVars<T extends Object>(envVarClass: ClassConstructor<T>): T {
  const envVars = plainToInstance(envVarClass, process.env, {
    enableImplicitConversion: true,
  });

  // Skip validation in development mode
  if (process.env.NODE_ENV === 'development' || process.env.SKIP_ENV_VALIDATION === 'true') {
    console.log('Skipping environment validation for development mode');
    return envVars;
  }

  const validationErrors = validateSync(envVars, { skipMissingProperties: false });

  if (validationErrors.length > 0) {
    throw new Error(validationErrors.toString());
  }

  return envVars;
}
