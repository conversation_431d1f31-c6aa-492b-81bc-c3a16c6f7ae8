AUTH0URL=https://sso.test.authrock.com/oauth/token
LAA_BASEURL=https://api.loan-access-authorizer.test.mortgageops.foc.zone
LAA_OAUTHAUDIENCE=urn:ql-api:loan_access_authorizer-202723:Test
RLAPI_BASEURL= https://api.test.np.rocket-logic.foc.zone/
RLAPI_OAUTHAUDIENCE= urn:ql-api:rocketlogicapi-210205:Test
RLB_BASEURL=https://test-rlb-orch.np.rocket-logic.foc.zone
RLB_OAUTHAUDIENCE=urn:ql-api:rlb_orchestrator-206156_test-206156:Test
RLA_FEEDBACK_BASEURL=https://rla-feedback-test.np.rocket-logic.foc.zone
RLA_FEEDBACK_OAUTHAUDIENCE=urn:ql-api:rla-feedback-219697:Test
EINSTEIN_HUB_BASEURL=https://api.einsteinhubv2.beta.eh-np.foc.zone
EINSTEIN_HUB_OAUTHAUDIENCE=urn:ql-api:einstein_hub-201646:Beta
EINSTEIN_HUB_APIKEY=
RDO_BASEURL=https://definition-orchestrator.test.rocket-trails-np.foc.zone
RDO_OAUTHAUDIENCE=urn:ql-api:rocket_trails_definition_orchestrator-206702:Test
CRM_BASEURL=https://api.credit-report-manager.test.credit-np.foc.zone
CRM_OAUTHAUDIENCE=urn:ql-api:credit-report-manager-209467:Test
LES_BASEURL=https://location-entity.test.rp.foc.zone
LES_OAUTHAUDIENCE=urn:ql-api:location_entity_service-203935:Test
LEGACY_ID_BRIDGE_BASEURL=https://test-rl-legacy-id-bridge.np.rocket-logic.foc.zone
LEGACY_ID_BRIDGE_OAUTHAUDIENCE=urn:ql-api:rocket-logic-legacy-id-bridge-212411:Test
SDM_BASEURL= https://sdm.test.servicing.foc.zone/
SDM_OAUTHAUDIENCE= urn:ql-api:sdm-api-201823:Test
LOPA_BASEURL= https://api.lopa.test.mortgageops.foc.zone/
LOPA_OAUTHAUDIENCE= urn:ql-api:loan-origination-api-206597:Test
LOPA_APIKEY=
MAPI_BASEURL= https://test-mortgageapi.launchpad-np.rkt.zone/
MAPI_OAUTHAUDIENCE= urn:ql-api:rocket_api_pe-210213:Test
JOB_RUNNER_BASEURL=https://api.rl-amp-job-rnr.test.np.rocket-logic.foc.zone/
JOB_RUNNER_OAUTHAUDIENCE=urn:ql-api:rl-amp-job-runner-211434:Test

PROPERTYTAXSERVICE_BASEURL=https://property-tax-service.test.ds-np.foc.zone/
PROPERTYTAXSERVICE_OAUTHAUDIENCE=urn:ql-api:property_tax_service-202088:Test
MESSAGE_CENTER_BASEURL=https://test-message-center-api.np.rocket-logic.foc.zone/
MESSAGE_CENTER_OAUTHAUDIENCE=urn:ql-api:message-center-api-212653:Test
FEES_CALC_BASEURL=https://feescalc.test.feeservices-np.foc.zone
FEES_CALC_OAUTHAUDIENCE=urn:ql-api:fees_calc_service-213181:Test
NO_TEST_ENV_AUTH0URL=https://sso.beta.authrock.com/oauth/token
NO_TEST_ENV_CLIENTID=
NO_TEST_ENV_CLIENTSECRET=
EINSTEIN_CALC_BASEURL=https://api.calc.v2.beta.einstein-np.foc.zone/
EINSTEIN_CALC_OAUTHAUDIENCE=urn:ql-api:einstein_calc-200856:Beta
Auth0_ClientId=
Auth0_ClientSecret=

TEAM_MEMBER_DATA_SERVICE_BASEURL=https://graphql-beta.tmds-np.foc.zone
TEAM_MEMBER_DATA_SERVICE_OAUTHAUDIENCE=urn:ql-api:tmds-nonprod-graphql-api-206427:Beta
OPEN_AMP_BASEURL=https://oagateway.test.openamp-np.foc.zone/rocketlogic
OPEN_AMP_OAUTHAUDIENCE=urn:ql-api:oagateway-208933:Test

PROPERTY_HUB_BASEURL=https://mulegentest.rktfoc.com/propertyhub/
MULESOFT_CLIENTID=
MULESOFT_CLIENTSECRET=

RL_CALL_SINK_BASEURL=https://rl-call-sink.np.rocket-logic.foc.zone
RL_CALL_SINK_OAUTHAUDIENCE=urn:ql-api:rl-call-sink-213408:Beta
NO_TEST_ENV_AUTH0URL=https://sso.beta.authrock.com/oauth/token
NO_TEST_ENV_CLIENTID=
NO_TEST_ENV_CLIENTSECRET=

REDIS_HOST=127.0.0.1
REDIS_PORT=6379

SQS_QUEUE_NAME=test-214291-rl-xp-bff
SQS_QUEUE_URL=https://sqs.us-east-2.amazonaws.com/************/test-214291-rl-xp-bff
TRANSPORT_ENABLED=false

KAFKA_USE_SSL_SASL=false
KAFKA_BROKER=127.0.0.1:29092

USER_INFO_URL=https://sso-test.rock-dev.auth0.com/userinfo
OVERRIDE_HEADERS=true
OVERRIDE_COMMON_ID=
OVERRIDE_UNIX_ID=
OVERRIDE_USERNAME=
OVERRIDE_AD_GROUPS=
OVERRIDE_EMAIL=

LAA_USE_V3_USERACCESS=true
LAA_USE_MOCK_DATA=true

#AWS_ENDPOINT_URL=http://localhost:4566  ## uncomment to use localstack
#S3_ENDPOINT=http://localhost:4566  ## uncomment to use localstack
S3_BUCKET_NAME_BANKER_FORM_DATA=test-214291-rl-xp-bff-banker-historical-form-data

DYNAMO_TABLE_NAME=test-214291-rl-xp-bff-db
AWS_PROFILE= ## non-prod sso profile name to connect to aws services locally
PRODUCT_SNAPSHOT_LIMIT=50
