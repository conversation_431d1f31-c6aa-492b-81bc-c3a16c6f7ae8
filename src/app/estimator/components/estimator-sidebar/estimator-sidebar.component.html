@if (selectedPricingOption(); as selectedPricingOption) {
  <div class="p-4 flex flex-col gap-4">
    <div class="rkt-Label-16 font-bold">Loan Info & Closing Fees</div>
    <div class="rkt-Label-14">
      <div class="text-green-600">{{selectedPricingOption.product.ProductCode}} - {{selectedPricingOption.product.ProductName}} {{selectedPricingOption.pricingOption.APR}} &#64; {{selectedPricingOption.pricingOption.Points.Final}} Points</div>
      @if (estimateAddress()?.data; as estimateLocation) {
        <div>Location: {{estimateLocation?.County?.Name}}, {{estimateLocation?.State}}</div>
      }
      <div>Loan Amount: {{loanAmount() | currency}}</div>
      <div>Principle & Interest: {{selectedPricingOption.pricingOption.PaymentInformation.PaymentInformationDetails.PrincipleAndInterestPayment | currency}}</div>
      <div>Mortgage Insurance Payment: {{selectedPricingOption.pricingOption.PaymentInformation.PaymentInformationDetails.MortgageInsurancePayment | currency}}</div>
      @if (taxAndInsuranceAmount(); as taxAndInsuranceAmount) {
        <div>Tax and Home Insurance: {{taxAndInsuranceAmount | currency}}</div>
      }
      <div>Total Monthly: {{selectedPricingOption.pricingOption.PaymentInformation.TotalPayment | currency}}</div>
      @if(ltvPercentageAmount(); as ltvPercentageAmount) {
        <div>LTV: {{ltvPercentageAmount}}%</div>
      }
      @if (estimateAddress()?.data?.County?.Limits; as estimateCountyLimits) {
        <div>FHA County Limit: {{estimateCountyLimits.FHALimit | currency}}</div>
        <div>Conv County Limit: {{estimateCountyLimits.ConventionalJumboLimit | currency}}</div>
      }
    </div>

    <mat-divider class="rkt-HorizontalDivider" />
    <div class="rkt-Label-16 font-bold">Adjustments</div>
    <div class="rkt-Label-14">
      @for (adjustment of selectedPricingOption.pricingOption.Points.Adjustments; track adjustment) {
        <div>Points {{ adjustment.Message }}: {{ adjustment.Value }}</div>
      }
      @for (adjustment of selectedPricingOption.pricingOption.Rate.Adjustments; track adjustment) {
        <div>Rate {{ adjustment.Message }}: {{ adjustment.Value }}</div>
      }
    </div>
    <mat-divider class="rkt-HorizontalDivider" />

    <label for="premiumInput" class="flex justify-between items-center">
      <div class="rkt-Body-16">Modify Premium (802)</div>
      <mat-form-field subscriptSizing="dynamic" class="[max-width:5rem]">
        <input
          id="premiumInput"
          class="rkt-Input"
          [formControl]="premiumCtrl"
          matInput
          appFormattedNumberInput
          [allowNegative]="true"
          [maxLength]="3"
          [decimalLimit]="0"
        />
        <span matTextSuffix>%</span>
      </mat-form-field>
    </label>

    <label for="originationInput" class="flex justify-between items-center">
      <div class="rkt-Body-16">Modify Origination (801)</div>
      <mat-form-field subscriptSizing="dynamic" class="[max-width:5rem]">
        <input
          id="originationInput"
          class="rkt-Input"
          [formControl]="originationCtrl"
          matInput
          appFormattedNumberInput
          [allowNegative]="true"
          [maxLength]="3"
          [decimalLimit]="0"
        />
        <span matTextSuffix>%</span>
      </mat-form-field>
    </label>
    @if (premiumCtrl.hasError('maxAmountExceeded')) {
      <mat-error class="text-wrap">{{getErrorMessage()}}</mat-error>
    }
    @if (hasCombinedFeeError()) {
      <mat-error class="text-wrap">Combined Loan Origination and Loan Discount Fee must be less than or equal to 10% of the loan amount.</mat-error>
    }
    <button
      class="rkt-Button rkt-Button--large"
      mat-flat-button
      color="primary"
      [class.rkt-Button--is-disabled]="hasCombinedFeeError() ||!hasValidFees()"
      [disabled]="hasCombinedFeeError() || !hasValidFees()"
      (click)="recalculateClosingFees()"
    >
      Recalculate Closing Fees
    </button>

    @if (feeDetails()?.data; as fees) {
      <div class="rkt-Label-14">Fee Details</div>
      <div class="fee-details">
        @if (productFees(); as productFees) {
          @for (fee of productFees; track fee) {
            <div class="flex justify-between items-center rkt-Body-14">
              <div>{{ getDisplayLabel(fee.Name) }}</div>
              @if (fee.Amount) {
                <div>{{ fee.Amount | currency }}</div>
              } @else {
                <div>-</div>
              }
            </div>
          }
        }
        @for (fee of fees; track fee) {
          <div class="flex justify-between items-center rkt-Body-14">
            <div class="mr-1">{{ fee.name }}</div>
            <div>{{ fee.value | currency }}</div>
          </div>
        }
        @if (totalFees(); as totalFees) {
          <div class="flex justify-between items-center rkt-Caption-14 font-bold">
            <div>Total Closing Fees</div>
            <div>{{ totalFees | currency }}</div>
          </div>
          }
      </div>
      <div class="total-caption rkt-Caption-10">
        Total excludes Reserves Deposited with Lender (1000 series HUD lines)
      </div>
    }
  </div>
}
