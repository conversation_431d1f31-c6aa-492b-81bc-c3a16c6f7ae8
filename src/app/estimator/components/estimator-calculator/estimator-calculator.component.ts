import { CommonModule, DatePipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { getErrorMessage } from '../../../../app/util/get-error-message';
import { TrackClickDirective } from '../../../analytics/track-click.directive';
import { HugeRadioGroupComponent } from '../../../huge-radio-group/huge-radio-group.component';
import { FormattedNumberInputComponent } from '../../../question-input/formatted-number-input/formatted-number-input.component';
import { TeamMemberDataService } from '../../../services/team-member-data/team-member-data.serivce';
import { OCCUPANCY } from '../../../util/occupancy';
import { PROPERTY } from '../../../util/property';
import { rawValueChanges$ } from '../../../util/raw-value-changes';
import { STATES } from '../../../util/states';
import { COMMITMENTPERIOD } from '../../models/commitment-period';
import { CREDITSCORE } from '../../models/credit-score';
import { EstimatorService } from '../../services/estimator.service';

@Component({
  selector: 'app-estimator-calculator',
  standalone: true,
  imports: [
    CommonModule,
    HugeRadioGroupComponent,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatSlideToggleModule,
    MatCardModule,
    MatDividerModule,
    DatePipe,
    FormattedNumberInputComponent,
    TrackClickDirective
  ],
  templateUrl: './estimator-calculator.component.html',
  styleUrl: './estimator-calculator.component.scss',
})
export class EstimatorCalculatorComponent {
  private estimatorService = inject(EstimatorService);
  private teamMemberDataService = inject(TeamMemberDataService);

  calcForm = this.estimatorService.calcForm;
  loanPurposeControl = this.estimatorService.loanPurposeControl;
  readonly LoanPurpose = LoanPurpose;

  productsResponse = toSignal(this.estimatorService.productsResponse$);
  canSearchProducts = toSignal(this.estimatorService.isCalcFormValid$);
  estimateDetails = toSignal(this.estimatorService.estimateDetails$);

  readonly propertyTypeOptions = PROPERTY;
  readonly creditScoreOptions = CREDITSCORE;
  readonly commitmentPeriodOptions = COMMITMENTPERIOD;
  readonly loanPurposeOptions = [LoanPurpose.Purchase, LoanPurpose.Refinance]
    .map((option) => ({ value: option, display: option }));
  readonly occupancyTypeOptions = OCCUPANCY;
  readonly stateOptions = STATES;

  loanPurpose = toSignal(rawValueChanges$(this.loanPurposeControl));

  counties = toSignal(this.estimatorService.counties$);

  teamMemberRoles = toSignal(this.teamMemberDataService.teamMemberRoles$);

  searchProducts() {
    this.estimatorService.searchProducts();
  }
  
  reset() {
    this.estimatorService.reset();
  }

  getErrorMessage() {
    return getErrorMessage(this.calcForm.controls.zipCode, [['required', () => 'Zip Code is required if State and County are empty.']]);
  }
  
  modifyEstimateDetails() {
    this.estimatorService.modifyEstimateDetails();
  }

  clearEstimateDetails() {
    this.estimatorService.clearEstimateDetails();
  }
}
