<div class="page-wrapper">
  <div class="container">
    <mat-card class="rkt-Card card rkt-Card--enterprise section-container">
      <div class="flex gap-6 self-stretch px-4 pt-4 pb-4">
        <div class="header-spacing">
          <mat-icon color="primary" svgIcon="calculate-outlined"/>
          <span class="rkt-Label-16 rkt-FontWeight--700 rkt-Spacing--ml8">CALCULATE</span>
        </div>
        @if (estimateDetails(); as estimateDetails) {
          <div class="flex justify-center items-center saved-pill">
            <mat-icon
              svgIcon="bookmark-outlined"
              class="rkt-Icon h-1 w-1 bookmark-color"
            ></mat-icon>
            <span class="rkt-FontWeight--500 saved-text">Saved Estimation</span>
          </div>
          <span class="last-saved">last saved {{estimateDetails.createdAt | date:'short'}}</span>
        }
      </div>
      <mat-card-content>
        <app-huge-radio-group class="select-none" [options]="loanPurposeOptions" [formControl]="loanPurposeControl" />
        <mat-divider class="rkt-HorizontalDivider"></mat-divider>
        <ng-container [formGroup]="calcForm">
          <div class="form-grid">
            <mat-form-field class="rkt-FormField col-span-5">
              <mat-label>Occupancy Type</mat-label>
              <mat-select class="rkt-Input" formControlName="occupancyType">
                @for (occupancyType of occupancyTypeOptions; track occupancyType) {
                  <mat-option [value]="occupancyType.value">{{ occupancyType.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-form-field class="rkt-FormField col-span-5">
              <mat-label>Property Type</mat-label>
              <mat-select class="rkt-Input" formControlName="propertyType">
                @for (propertyType of propertyTypeOptions; track propertyType) {
                  <mat-option [value]="propertyType.value">{{ propertyType.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-form-field class="rkt-FormField col-span-5">
              <mat-label>Property State</mat-label>
              <mat-select class="rkt-Input" formControlName="state">
                @for (state of stateOptions; track state) {
                  <mat-option [value]="state.value">{{ state.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-form-field class="rkt-FormField col-span-5">
              <mat-label>Property County</mat-label>
              <mat-select class="rkt-Input" formControlName="countyName">
                @for (county of counties(); track county) {
                  <mat-option [value]="county.name">{{ county.name }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <mat-form-field class="rkt-FormField col-span-5">
              <mat-label>Property Zip</mat-label>
              <input
                matInput
                formControlName="zipCode"
                placeholder="Enter ZIP Code"
                class="rkt-Input"
              />
              <mat-error>{{ getErrorMessage() }}</mat-error>
            </mat-form-field>
            @if (loanPurpose() === LoanPurpose.Purchase) {
              <app-formatted-number-input
                class="col-span-5"
                label="Purchase Price"
                prefix="$"
                [control]="calcForm.controls.purchasePrice"
                [allowNegative]="false"
              />
              <app-formatted-number-input
                class="col-span-3"
                label="Down Payment"
                prefix="$"
                [control]="calcForm.controls.downPaymentAmount"
                [allowNegative]="false"
              />
              <app-formatted-number-input
                class="col-span-4"
                label="Down Payment Percentage"
                suffix="%"
                [control]="calcForm.controls.downPaymentPercentage"
                [allowNegative]="false"
              />
              <app-formatted-number-input
                class="col-span-3"
                label="Loan Amount"
                prefix="$"
                [control]="calcForm.controls.loanAmount"
                [allowNegative]="false"
              />
            }
            @if (loanPurpose() === LoanPurpose.Refinance) {
              <app-formatted-number-input
                class="col-span-5"
                label="Estimated Property Value"
                prefix="$"
                [control]="calcForm.controls.estimatedPropertyValue"
                [allowNegative]="false"
              />
              <mat-slide-toggle
                class="rkt-SlideToggle col-span-2"
                color="accent"
                formControlName="isCashout"
              >
                <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Cash Out</span>
              </mat-slide-toggle>
              <app-formatted-number-input
                class="col-span-4"
                label="Current Loan Amount"
                prefix="$"
                [control]="calcForm.controls.currentLoanAmount"
                [allowNegative]="false"
              />
              <app-formatted-number-input
                class="col-span-4"
                label="Desired Cashout Amount"
                prefix="$"
                [control]="calcForm.controls.desiredCashoutAmount"
                [allowNegative]="false"
              />
            }
            <mat-form-field class="col-span-4 rkt-FormField">
              <mat-label for="name">Estimated Credit Score</mat-label>
              <mat-select class="rkt-Input" formControlName="creditScore">
                @for (creditScore of creditScoreOptions; track creditScore) {
                  <mat-option [value]="creditScore.value">{{ creditScore.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            <app-formatted-number-input
              class="col-span-3"
              label="Annual Income"
              prefix="$"
              [control]="calcForm.controls.annualIncome"
              [allowNegative]="false"
            />
            <mat-form-field class="rkt-FormField col-span-3">
              <mat-label for="name">Commitment Period</mat-label>
              <mat-select class="rkt-Input" formControlName="commitmentPeriod">
                @for (period of commitmentPeriodOptions; track period) {
                  <mat-option [value]="period.value">{{ period.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
            @if (teamMemberRoles()?.isSchwabBanker) {
              <app-formatted-number-input
                class="col-span-5"
                label="Qualifying Schwab Account Balances"
                prefix="$"
                [control]="calcForm.controls.schwabBalance"
                [allowNegative]="false"
              />
            }
            <mat-slide-toggle
              class="rkt-SlideToggle col-span-2"
              color="accent"
              formControlName="isMilitary">
              <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Military</span>
            </mat-slide-toggle>
            <mat-slide-toggle
              class="rkt-SlideToggle col-span-3"
              color="accent"
              formControlName="isEscrowWaived">
              <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Escrow Waived</span>
            </mat-slide-toggle>
            @if(teamMemberRoles()?.isSchwabBanker) {
              <mat-slide-toggle
                class="rkt-SlideToggle col-span-3"
                color="accent"
                formControlName="isRIAClient">
                <span class="rkt-SlideToggle__label rkt-Spacing--ml8">RIA Client</span>
              </mat-slide-toggle>
            }
            @if (loanPurpose() === LoanPurpose.Purchase) {
              <mat-slide-toggle
                class="rkt-SlideToggle col-span-4"
                color="accent"
                formControlName="isFirstTimeHomeBuyer">
                <span class="rkt-SlideToggle__label rkt-Spacing--ml8">First Time Home Buyer</span>
              </mat-slide-toggle>
            }
          </div>
        </ng-container>
        <div class="flex justify-between mt-5">
          <div>
            @if (estimateDetails(); as estimateDetails)
            {
              @if (calcForm.disabled) {
                <button
                  mat-flat-button
                  class="rkt-Button rkt-Button--secondary rkt-Button--large mr-2"
                  (click)="modifyEstimateDetails()">
                  Modify
                  <mat-icon
                    svgIcon="lock_open-outlined"    
                    class="rkt-Icon"
                  />
                </button>
              }
              <button
                mat-flat-button
                class="rkt-Button rkt-Button--secondary rkt-Button--large mr-2"
                (click)="clearEstimateDetails()">
                New Estimate
              </button>
            }
          </div>
          <div>
            @if (!calcForm.disabled) {
              <button
                mat-flat-button
                class="rkt-Button rkt-Button--secondary rkt-Button--large mr-2"
                (click)="reset()">
                Reset
              </button>
            }
            <button
              appTrackClick
              description="Get Pricing Estimate"
              [disabled]="!canSearchProducts() || productsResponse()?.loading"
              [class.rkt-Button--is-disabled]="!canSearchProducts() || productsResponse()?.loading"
              class="rkt-Button rkt-Button--large"
              mat-flat-button color="primary"
              (click)="searchProducts()">
              Get Pricing
            </button>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
