import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { NonNullableFormBuilder, Validators } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { LoanPurpose, OccupancyType, State } from '@rocket-logic/rl-xp-bff-models';
import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';
import { BehaviorSubject, combineLatest, concat, defer, EMPTY, filter, firstValueFrom, map, mergeMap, Observable, of, shareReplay, switchMap, take, withLatestFrom } from 'rxjs';
import { environment } from '../../../environments/environment';
import { DataProviderService } from '../../services/data-provider/data-provider.service';
import { TeamMemberDataService, TeamMemberRoles } from '../../services/team-member-data/team-member-data.serivce';
import { loadable, Loadable } from '../../util/loadable';
import { rawValueChanges$ } from '../../util/raw-value-changes';
import { ProductFees } from '../components/estimator-sidebar/estimator-sidebar.component';
import { EstimateDetails } from '../models/estimate-details';
import { FeesCalcResponse } from '../models/fees-calc-response';
import { AprFee, EstimateToShare, Fee, FeeAdjustments, FtdDetails, ParentEligibilityGroup, PricingOption, Product, ProductsResponse } from '../models/products';
import { mockEhResponse } from './mock-eh-response';
import { mockFeesResponse } from './mock-fees-response';

export enum EstimatorStep {
  Calculator = 'CALCULATOR',
  Estimate = 'ESTIMATE',
}

export interface SelectedPricingOption {
  productName: string;
  productCode: string;
  product: Product;
  pricingOption: PricingOption;
}

export type BaseEstimateRequest = {
  occupancyType: string,
  propertyType: string,
  zipCode: string,
  countyName: string,
  state: string,
  creditScore: number,
  schwabBalance: number,
  isMilitary: boolean,
  isEscrowWaived: boolean,
  isRIAClient: boolean,
  annualIncome: number,
  commitmentPeriod: number
};

export type PurchaseEstimateRequest = BaseEstimateRequest & {
  purchasePrice: number,
  downPaymentAmount: number,
  downPaymentPercentage: number,
  loanAmount: number,
  isCashout: false,
  isFirstTimeHomeBuyer: boolean,
}

export type RefinanceEstimateRequest = BaseEstimateRequest & {
  currentLoanAmount: number,
  desiredCashoutAmount: number,
  estimatedPropertyValue: number,
  isCashout: boolean,
  isFirstTimeHomeBuyer: false,
}

export type EstimateRequest = PurchaseEstimateRequest | RefinanceEstimateRequest;

@Injectable({ providedIn: 'root' })
export class EstimatorService {
  private fb = inject(NonNullableFormBuilder);
  private httpClient = inject(HttpClient);
  private BASE_URL = environment.assistantUrl; // @TODO change back
  private dataProviderService = inject(DataProviderService);
  private router = inject(Router);
  private teamMemberDataService = inject(TeamMemberDataService);

  private isOpenFeesSidebarSubject = new BehaviorSubject<boolean>(false);
  isOpenFeesSidebar$ = this.isOpenFeesSidebarSubject.asObservable();
  private selectedPricingOptionIdSubject = new BehaviorSubject<{ pricingOptionId: PricingOption['Id'], productCode: Product['ProductCode'] } | null>(null);
  selectedPricingOptionId$ = this.selectedPricingOptionIdSubject.asObservable();

  private productsRequestSubject = new BehaviorSubject<EstimateRequest | null>(null);
  productsRequest$ = this.productsRequestSubject.asObservable();

  private feeAdjustmentsSubject = new BehaviorSubject<FeeAdjustments[]>([]);
  feeAdjustments$ = this.feeAdjustmentsSubject.asObservable();

  private checkedPricingOptionIdsSubject = new BehaviorSubject<Array<{ pricingOptionId: PricingOption['Id'], productCode: Product['ProductCode'] }>>([]);
  checkedPricingOptionIds$ = this.checkedPricingOptionIdsSubject.asObservable();

  private loanAmountSubject = new BehaviorSubject<number | null>(null);
  loanAmount$: Observable<number | null> = this.loanAmountSubject.asObservable();

  private activePageSubject = new BehaviorSubject<EstimatorStep>(EstimatorStep.Calculator);
  activePage$ = this.activePageSubject.asObservable();

  opportunityIdUrlPattern = new RegExp('estimator\\/(?<opportunityId>.+)');
  opportunityId$ = this.router.events.pipe(
    filter((event): event is NavigationEnd => event instanceof NavigationEnd),
    map(event => {
      const matches = event.url.match(this.opportunityIdUrlPattern);
      const opportunityId = matches?.groups?.['opportunityId'];
      return opportunityId ?? null;
    }),
    shareReplay(1),
  );

  loanPurposeControl = this.fb.control<LoanPurpose>(LoanPurpose.Purchase);

  calcForm = this.fb.group({
    occupancyType: this.fb.control<OccupancyType | null>(null, [Validators.required]),
    propertyType: this.fb.control<PropertyType | null>(null, [Validators.required]),
    zipCode: this.fb.control<string | null>(null),
    countyName: this.fb.control<string | null>(null),
    state: this.fb.control<State | null>(null),
    purchasePrice: this.fb.control<number | null>(null),
    creditScore: this.fb.control<number | null>(null, [Validators.required]),
    downPaymentAmount: this.fb.control<number | null>(null),
    downPaymentPercentage: this.fb.control<number | null>(null),
    loanAmount: this.fb.control<number | null>(null),
    schwabBalance: this.fb.control<number | null>(null),
    isCashout: this.fb.control<boolean>(false),
    isMilitary: this.fb.control<boolean>(false),
    isEscrowWaived: this.fb.control<boolean>(false),
    currentLoanAmount: this.fb.control<number | null>(null),
    estimatedPropertyValue: this.fb.control<number | null>(null),
    isRIAClient: this.fb.control<boolean>(false),
    desiredCashoutAmount: this.fb.control<number | null>(null),
    annualIncome: this.fb.control<number | null>(null),
    isFirstTimeHomeBuyer: this.fb.control<boolean>(false),
    commitmentPeriod: this.fb.control<number>(45, [Validators.required]),
  });

  isPurchaseRequest = (input: EstimateRequest): input is Extract<EstimateRequest, { purchasePrice: number }> => {
    return 'purchasePrice' in input && input.purchasePrice !== null && this.loanPurposeControl.value === LoanPurpose.Purchase;
  };

  constructor() {
    this.estimateDetails$.pipe(
      filter((x): x is EstimateDetails => !!x),
      take(1),
    ).subscribe((estimateDetails) => {
      if (estimateDetails) {
        this.calcForm.patchValue({
          occupancyType: estimateDetails.calcInputs?.occupancyType,
          propertyType: estimateDetails.calcInputs?.propertyType,
          state: estimateDetails.calcInputs?.state,
          purchasePrice: estimateDetails.calcInputs?.purchasePrice,
          creditScore: estimateDetails.calcInputs?.creditScore,
          downPaymentPercentage: estimateDetails.calcInputs?.downPaymentPercentage,
          loanAmount: estimateDetails.calcInputs?.loanAmount,
          schwabBalance: estimateDetails.calcInputs?.schwabBalance,
          isCashout: estimateDetails.calcInputs?.isCashout,
          isMilitary: estimateDetails.calcInputs?.isMilitary,
          isEscrowWaived: estimateDetails.calcInputs?.isEscrowWaived,
          currentLoanAmount: estimateDetails.calcInputs?.currentLoanAmount,
          estimatedPropertyValue: estimateDetails.calcInputs?.estimatedPropertyValue,
          isRIAClient: estimateDetails.calcInputs?.isRIAClient,
          desiredCashoutAmount: estimateDetails.calcInputs?.desiredCashoutAmount,
          zipCode: estimateDetails.calcInputs?.zipCode,
          annualIncome: estimateDetails.calcInputs?.annualIncome,
          isFirstTimeHomeBuyer: estimateDetails.calcInputs?.isFirstTimeHomeBuyer
        });
        this.calcForm.patchValue({countyName: estimateDetails.calcInputs?.countyName});
        this.loanPurposeControl.patchValue(estimateDetails.loanPurpose as LoanPurpose);
        this.calcForm.disable({emitEvent: false});
        this.loanPurposeControl.disable({emitEvent: false});
      }
    });
    this.calcForm.controls.loanAmount.disable({emitEvent: false});
    this.calcForm.controls.countyName.disable({emitEvent: false});
    const { purchasePrice, downPaymentAmount, downPaymentPercentage, estimatedPropertyValue, currentLoanAmount, desiredCashoutAmount, isFirstTimeHomeBuyer } = this.calcForm.controls;
    const purchaseControls = [purchasePrice, downPaymentAmount, downPaymentPercentage, isFirstTimeHomeBuyer];
    const refinanceControls = [estimatedPropertyValue, currentLoanAmount, desiredCashoutAmount];

    rawValueChanges$(this.calcForm.controls.zipCode).subscribe(() => {
      this.addressChangeValidation();
    });
    rawValueChanges$(this.calcForm.controls.state).subscribe(() => {
      if (this.calcForm.controls.state.enabled) {
        this.calcForm.controls.countyName.setValue(null, {emitEvent: false});
        this.calcForm.controls.zipCode.setValue(null, {emitEvent: false});
        if (this.calcForm.controls.state.value) {
          this.calcForm.controls.countyName.enable({emitEvent: false})
        }
        this.addressChangeValidation();
      }
    });
    rawValueChanges$(this.calcForm.controls.countyName).subscribe(() => {
     this.addressChangeValidation();
    });

    rawValueChanges$(this.calcForm.controls.isCashout).subscribe(value => {
      value ? this.calcForm.controls.desiredCashoutAmount.enable({emitEvent: false}) : this.calcForm.controls.desiredCashoutAmount.disable({emitEvent: false})
      this.calcForm.controls.desiredCashoutAmount.setValue(null, {emitEvent: false});
      if (this.calcForm.controls.isCashout.value) {
        this.calcForm.controls.currentLoanAmount.clearValidators();
      } else {
        this.calcForm.controls.currentLoanAmount.setValidators(Validators.required);
      }
      this.calcForm.controls.currentLoanAmount.updateValueAndValidity();
    });

    rawValueChanges$(this.loanPurposeControl).subscribe(value => {
      const controlsToAddValidator = value === LoanPurpose.Purchase ? purchaseControls : refinanceControls;
      const controlsToRemoveValidator = value === LoanPurpose.Purchase ? refinanceControls : purchaseControls;
      controlsToAddValidator.forEach(control => {
        control.setValidators(Validators.required);
        control.updateValueAndValidity();
      });
      controlsToRemoveValidator.forEach(control => {
        control.clearValidators();
        control.updateValueAndValidity();
      });
      this.calcForm.controls.downPaymentAmount.enable({emitEvent: false});
      this.calcForm.controls.downPaymentPercentage.enable({emitEvent: false});
    });

    rawValueChanges$(this.calcForm.controls.downPaymentAmount).subscribe(value => {
      value ? this.calcForm.controls.downPaymentPercentage.disable({emitEvent: false}) : this.calcForm.controls.downPaymentPercentage.enable({emitEvent: false});
      const purchasePrice = this.calcForm.controls.purchasePrice.value;
      const downPaymentAmount = this.calcForm.controls.downPaymentAmount.value;
      const downPaymentPercentage = purchasePrice !== null
        ? downPaymentAmount !== null
          ? (downPaymentAmount / purchasePrice) * 100
          : null
        : null;
      
      this.calcForm.controls.downPaymentPercentage.setValue(downPaymentPercentage, {emitEvent: false});
    });
    rawValueChanges$(this.calcForm.controls.downPaymentPercentage).subscribe(value => {
      value ? this.calcForm.controls.downPaymentAmount.disable({emitEvent: false}) : this.calcForm.controls.downPaymentAmount.enable({emitEvent: false});
      const purchasePrice = this.calcForm.controls.purchasePrice.value;
      const downPaymentPercentage = this.calcForm.controls.downPaymentPercentage.value;
      const downPaymentAmount = purchasePrice !== null
        ? downPaymentPercentage !== null
          ? (downPaymentPercentage / 100) * purchasePrice
          : null
        : null;

      this.calcForm.controls.downPaymentAmount.setValue(downPaymentAmount, {emitEvent: false});
    });
    rawValueChanges$(this.calcForm.controls.purchasePrice).subscribe(purchasePrice => {
      const downPaymentPercentage = this.calcForm.controls.downPaymentPercentage.value;
      const downPaymentAmount = purchasePrice !== null
        ? downPaymentPercentage !== null
          ? (downPaymentPercentage / 100) * purchasePrice
          : null
        : null;

      this.calcForm.controls.downPaymentAmount.setValue(downPaymentAmount, {emitEvent: false});
    });

    rawValueChanges$(this.calcForm).subscribe(() => {
      const purchasePrice = this.calcForm.controls.purchasePrice.value;
      const downPaymentAmount = this.calcForm.controls.downPaymentAmount.value;
      const downPaymentPercentage = this.calcForm.controls.downPaymentPercentage.value;

      const loanAmount = purchasePrice !== null
        ? downPaymentAmount !== null
          ? purchasePrice - downPaymentAmount
          : downPaymentPercentage !== null
            ? purchasePrice - (downPaymentPercentage / 100) * purchasePrice
            : null
        : null;

      this.calcForm.controls.loanAmount.setValue(loanAmount, {emitEvent: false});
    });

    if (environment.estimator?.useMockData) {
      this.calcForm.patchValue({
        occupancyType: OccupancyType.PrimaryResidence,
        propertyType: PropertyType.SingleFamily,
        zipCode: '48603',
        countyName: 'Saginaw',
        state: State.Michigan,
        purchasePrice: 200_000,
        creditScore: 720,
        downPaymentPercentage: 10,
        loanAmount: 150_000,
        schwabBalance: 1000,
        isCashout: false,
        isMilitary: false,
        isEscrowWaived: false,
        currentLoanAmount: null,
        estimatedPropertyValue: null,
        isRIAClient: false,
        desiredCashoutAmount: null,
        annualIncome: 100000,
      });
      this.loanPurposeControl.patchValue(LoanPurpose.Purchase);
    }
  }

  private addressChangeValidation() {
    const zipCodeControl = this.calcForm.controls.zipCode;
    const stateControl = this.calcForm.controls.state;
    const countyNameControl = this.calcForm.controls.countyName;
    if (!zipCodeControl?.value && (!stateControl?.value || !countyNameControl?.value)) {
      zipCodeControl?.setValidators(Validators.required);
    } else {
      zipCodeControl?.clearValidators();
    }
    zipCodeControl?.updateValueAndValidity({emitEvent: false});
  }

  counties$ = rawValueChanges$(this.calcForm.controls.state).pipe(
    switchMap((value) => {
      if (!value) return of([]);
      return this.dataProviderService.getCountiesByState$(value);
    }),
    shareReplay(1),
  );

  productsResponse$: Observable<Loadable<ProductsResponse>> = this.productsRequestSubject.pipe(
    filter((input): input is EstimateRequest => input !== null),
    withLatestFrom(this.teamMemberDataService.teamMemberRoles$),
    switchMap(([estimateRequest, teamMemberRoles]) => {
      const loanPurpose = this.loanPurposeControl.value;
      const subjectProperty = this.buildSubjectProperty(estimateRequest, loanPurpose);
      const configuration = this.buildRequestConfiguration(estimateRequest, loanPurpose, teamMemberRoles);
      const isPurchaseRequest = this.isPurchaseRequest(estimateRequest);
      const downPaymentAmount = isPurchaseRequest
        ? estimateRequest.downPaymentAmount ?? (estimateRequest.downPaymentPercentage / 100) * estimateRequest.purchasePrice
        : 0;
      const clients = [
        {
          CreditScore: estimateRequest.creditScore,
          LivingSituation: {
            FirstTimeHomeBuyer: isPurchaseRequest ? estimateRequest.isFirstTimeHomeBuyer : false,
          },
          AssetCollection: [
            {
              balance: isPurchaseRequest ? 1000000 + downPaymentAmount : 1000000, //EH default asset if none are provided
              type: "Checking",
              desiredAmountToApply: isPurchaseRequest ? 1000000 + downPaymentAmount : 1000000,
            },
            ...(teamMemberRoles.isSchwabBanker ? [{
              EligibleBalances: {
                "Schwab": estimateRequest.schwabBalance,
              },
              type: "PartnerAsset",
              desiredAmountToApply: estimateRequest.schwabBalance,
            }] : [])
          ],
          VaInfo: estimateRequest.isMilitary ? {
            serviceType: "RegularMilitary"
          } : null,
          RIAClient: teamMemberRoles.isSchwabBanker ? estimateRequest.isRIAClient : false,
          ...(estimateRequest.annualIncome !== null ? {
            IncomeCollection: [
              {
                AnnualAmount: estimateRequest.annualIncome,
                IncomeType: "Employment",
                IsFullTime: true,
                IsPartTime: false
              }
            ],
          } : {})
        }
      ];
      const fees = {
        FHA: {
          NonAprFees: 1,
          AprFees: 0.0,
          Texas50a6Fees: 1
        },
        Conventional: {
            NonAprFees: 1,
            AprFees: 0.0,
            Texas50a6Fees: 1
        },
        VA: {
            NonAprFees: 1,
            AprFees: 0.0,
            Texas50a6Fees: 1
        },
        Schwab: {
            NonAprFees: 1,
            AprFees: 0.0,
            Texas50a6Fees: 1
        },
        HELOC: {
            NonAprFees: 1,
            AprFees: 0.0,
            Texas50a6Fees: 1
        },
        Jumbo: {
            NonAprFees: 1,
            AprFees: 0.0,
            Texas50a6Fees: 1
        }
      }

      const productOptions = {
        Channel: teamMemberRoles.isSchwabBanker ? 'Cadillac': 'None',
        SelectionTargets: {
          ProductTargets: {
            OneProductPerTerm: false,
            IncludeInEligibleProducts: true,
          },
          PricingOptionTargets:{
            IncludeIneligibleOptions: true,
          }
        }
      }

      const request = isPurchaseRequest ? {
        purchasePrice:  estimateRequest.purchasePrice,
        downPaymentAmount,
        configuration,
        subjectProperty,
        clients,
        productOptions,
        commitmentPeriod: estimateRequest.commitmentPeriod
      } : {
        DesiredCashoutAmount: estimateRequest.isCashout ? estimateRequest.desiredCashoutAmount : null,
        DesiredGoal: estimateRequest.isCashout ? "Cashout" : "LowerPayment",
        IsStudentLoanCashout: false,
        IsDivorceCashout: false,
        configuration,
        subjectProperty,
        clients,
        fees,
        productOptions,
        SameServicerHARPEligibility: "None",
      }

      const source$ = environment.estimator?.useMockData
        ? of(mockEhResponse)
        : this.getProducts$(request, loanPurpose as string);
      return source$.pipe(
        loadable(),
      );
    }),
    shareReplay(1),
  );

  checkedPricingOptions$ = combineLatest([
    this.checkedPricingOptionIds$,
    this.productsResponse$,
  ]).pipe(
    map(([checkedPricingOptionIds, productsResponse]) => {
      const allProducts = productsResponse.data?.ProductGroups.flatMap(group => group.Products) ?? [];
      const checkedPricingOptions = checkedPricingOptionIds
        .map(option => {
          const product = allProducts.find(product => product.ProductCode === option.productCode);
          if (!product) return null;
          const pricingOption = product.PricingOptions.find(pricingOption => pricingOption.Id === option.pricingOptionId);
          if (!pricingOption) return null;
          return { pricingOption, product };
        })
        .filter((x): x is { pricingOption: PricingOption, product: Product } => x !== null);
      return checkedPricingOptions;
    }),
    shareReplay(1),
  );

  selectedPricingOption$ = combineLatest([
    this.selectedPricingOptionId$,
    this.productsResponse$,
  ]).pipe(
    map(([selectedPricingOptionId, productsResponse])=> {
      if (selectedPricingOptionId === null) return null;
      const allProducts = productsResponse.data?.ProductGroups.flatMap(group => group.Products) ?? [];
      const product = allProducts.find(product => product.ProductCode === selectedPricingOptionId.productCode);
      if (!product) return null;
      const pricingOption = product.PricingOptions.find(pricingOption => pricingOption.Id === selectedPricingOptionId.pricingOptionId);
      if (!pricingOption) return null;
      return { pricingOption, product };
    }),
    shareReplay(1),
  )

  canShareEstimate$: Observable<boolean> = this.checkedPricingOptionIds$.pipe(
    map(pricingOptions => pricingOptions.length > 0),
    shareReplay(1),
  );

  estimatesToShare$: Observable<EstimateToShare[]> = combineLatest([
    this.checkedPricingOptions$,
    this.feeAdjustments$,
    this.teamMemberDataService.teamMemberRoles$
  ]).pipe(
    mergeMap(async ([checkedPricingOptions, feeAdjustments, teamMemberRoles]) => {
      const promises = checkedPricingOptions.map(async checkedOption => {
        const pricingOptionFees = this.getProductFees(checkedOption.product.ParentEligibilityGroups, checkedOption.pricingOption.FundsToClose.Details, teamMemberRoles);
        // Find matching fee adjustments for the current pricing option
        const adjustments = feeAdjustments.find(adj => adj.optionId === checkedOption.pricingOption.Id);
        const productFees = {
          ...pricingOptionFees,
          ...adjustments?.productFees,
        };
        const productFeesToShare = Object.values(productFees).filter((fee): fee is AprFee => fee !== null);
        const closingFees = await this.getClosingFees(checkedOption.product, checkedOption.pricingOption);
        const totalFees = this.getTotalFees(productFeesToShare, closingFees);

        return {
          pricingOption: checkedOption.pricingOption,
          productName: checkedOption.product.ProductName,
          loanAmount: this.loanAmountSubject.value ?? 0,
          productFees: productFeesToShare,
          feeDetails: closingFees,
          totalFees: totalFees
        } satisfies EstimateToShare;
      });
      const estimates = await Promise.all(promises);
      return estimates;
    }),
    shareReplay(1),
  );

  isCalcFormValid$ = concat(
    defer(() => of(this.calcForm.valid)),
    this.calcForm.statusChanges.pipe(
      map((status) => status === 'VALID'),
    ),
  ).pipe(
    shareReplay(1),
  );

  estimateDetails$: Observable<EstimateDetails | null> = this.opportunityId$.pipe(
      switchMap(id => id ? this.getEstimateDetails$(id) : of(null)),
      shareReplay(1)
  )

  searchProducts() {
    if (this.loanPurposeControl.value === LoanPurpose.Purchase) {
      this.loanAmountSubject.next(this.calcForm.controls.loanAmount.value);
    } else if (this.loanPurposeControl.value === LoanPurpose.Refinance && this.calcForm.controls.isCashout.value === true) {
      const currentLoanAmount = this.calcForm.controls.currentLoanAmount.value || 0;
      const desiredCashoutAmount = this.calcForm.controls.desiredCashoutAmount.value || 0;
      this.loanAmountSubject.next(desiredCashoutAmount + currentLoanAmount);
    } else {
      this.loanAmountSubject.next(this.calcForm.controls.currentLoanAmount.value);
    }
    this.isOpenFeesSidebarSubject.next(false);
    this.selectedPricingOptionIdSubject.next(null);
    this.productsRequestSubject.next(this.calcForm.value as EstimateRequest);
  }

  reset() {
    this.calcForm.reset();
    this.loanPurposeControl.reset();
    this.calcForm.controls.countyName.disable({emitEvent: false});
    this.isOpenFeesSidebarSubject.next(false);
    this.productsRequestSubject.next(null);
    this.loanAmountSubject.next(null);
  }

  selectPricingOption(pricingOptionId: string, productCode: string): void {
    const currentSelectedOption = this.selectedPricingOptionIdSubject.value;
    const isSameOptionSelected = currentSelectedOption && pricingOptionId === currentSelectedOption.pricingOptionId;

    //if no option selected or different option selected
    if (!currentSelectedOption || !isSameOptionSelected) {
      this.selectedPricingOptionIdSubject.next({pricingOptionId: pricingOptionId, productCode: productCode });
      this.toggleFeesSidebar(true);
    //if same option selected
    } else {
      this.selectedPricingOptionIdSubject.next(null);
      this.toggleFeesSidebar(!this.isOpenFeesSidebarSubject.value);
    }
  }

  addFeesWithAdjustments(feesWithAdjustments: FeeAdjustments) {
    const currentFeesWithAdjustments = this.feeAdjustmentsSubject.value;
    const existingIndex = currentFeesWithAdjustments.findIndex(fees => fees.optionId === feesWithAdjustments.optionId)
    if (existingIndex === -1 ) {
      // add new feesWithAdjustments
      this.feeAdjustmentsSubject.next([...currentFeesWithAdjustments, feesWithAdjustments]);
    } else {
      // update existing feesWithAdjustments
      const updatedfeesWithAdjustments = currentFeesWithAdjustments.map(fees =>
        fees.optionId === feesWithAdjustments.optionId
          ? feesWithAdjustments
          : fees
      );
      this.feeAdjustmentsSubject.next(updatedfeesWithAdjustments);
    }
  }

  toggleFeesSidebar(isOpen: boolean) {
    this.isOpenFeesSidebarSubject.next(isOpen);
  }

  updateCheckedPricingOption(checked: boolean, pricingOption: PricingOption, product: Product) {
    const checkedOptions = this.checkedPricingOptionIdsSubject.value;

    if (checked) {
      const existing = checkedOptions.find(checkedOption => checkedOption.pricingOptionId === pricingOption.Id);
      if (!existing) {
        // add new pricing option to checked
        this.checkedPricingOptionIdsSubject.next([...checkedOptions, { pricingOptionId: pricingOption.Id, productCode: product.ProductCode }]);
      } else {
        // update existing pricing option
        const updatedOptions = checkedOptions.map(checkedOption =>
          checkedOption.pricingOptionId === pricingOption.Id
            ? { pricingOptionId: pricingOption.Id, productCode: product.ProductCode }
            : checkedOption
        );
        this.checkedPricingOptionIdsSubject.next(updatedOptions);
      }
    } else {
        // remove already checked pricing option
        const updatedOptions = checkedOptions.filter(checkedOption => checkedOption.pricingOptionId !== pricingOption.Id);
        this.checkedPricingOptionIdsSubject.next(updatedOptions);
    }
  }

  getProducts$(body: any, loanPurpose: string): Observable<any> {
    return this.httpClient.post(`${this.BASE_URL}/estimator/products`, body, {
      params: { loanPurpose }
    });
  }

  private readonly feeNumbersToExclude = new Set<string>(['801', '802', '810']);

  getClosingFees$(
    product: Product,
    pricingOption: PricingOption
  ): Observable<Fee[]> {
    return this.productsResponse$.pipe(
      take(1),
      filter(productsResponse => !!productsResponse.data),
      withLatestFrom(this.teamMemberDataService.teamMemberRoles$),
      switchMap(([productsResponse, teamMemberRoles]) => {
        const form = this.calcForm.value;

        const loanPurpose = this.loanPurposeControl.value;
        const { zipCode, occupancyType, propertyType, estimatedPropertyValue, desiredCashoutAmount, currentLoanAmount, isCashout } = form;

        const feesCalcPropertyType = (() => {
          switch (propertyType) {
            case PropertyType.SingleFamily: return 'Single Family';
            case PropertyType.PUD: return 'PUD';
            case PropertyType.TwoToFourFamily: return '2-4 Family';
            case PropertyType.Coop: return 'CO-OP';
            case PropertyType.ManufacturedPUD: return 'Manf Home/PUD';
            case PropertyType.SiteCondo: return 'Site Condominium';
            case PropertyType.Condominium: return 'Condominium';
            case PropertyType.ManufacturedSingleFamily: return 'Manufactured Home';
            case PropertyType.Townhouse: return 'Townhouse';
            // PropertyType.ManufacturedCondominium: return 'Manufactured Home'; // @TODO need to confirm
            default: return null;
          }
        })();

        const feesCalcOccupancyType = (() => {
          switch (occupancyType) {
            case OccupancyType.PrimaryResidence: return 'primary residence';
            case OccupancyType.SecondHome: return 'second home';
            case OccupancyType.Investment: return 'investment property';
            default: return null;
          }
        })();

        if (
          !loanPurpose ||
          !feesCalcOccupancyType ||
          !feesCalcPropertyType ||
          !productsResponse.data
        ) {
          // @TODO show snackbar.
          return EMPTY;
        }

        const loanTypeGroups = [ParentEligibilityGroup.Conventional, ParentEligibilityGroup.FHA, ParentEligibilityGroup.VA];

        const loanType = product.ParentEligibilityGroups.find(group => loanTypeGroups.includes(group as ParentEligibilityGroup));
        const division = loanType === ParentEligibilityGroup.Conventional ? ParentEligibilityGroup.Conventional : ParentEligibilityGroup.Government;

        const refinanceLoanAmount = loanPurpose === LoanPurpose.Refinance && isCashout ? desiredCashoutAmount : currentLoanAmount;
        const desiredLoanAmount = loanPurpose === LoanPurpose.Purchase ? this.calcForm.controls.loanAmount.value : refinanceLoanAmount;

        const baseFeeFactors = {
          LoanChannel: teamMemberRoles.isSchwabBanker ? 'Cadillac' : 'None',
          LoanPurpose: loanPurpose,

          LoanType: loanType, // could be Conventional, FHA, USDA, VA. https://git.rockfin.com/Einstein/einstein-hub-v2/blob/3a2efcf6f7a3989fbef728d747e1e9138ed6aecb/EinsteinHub.Defaults/Mappings/Fees/FeeMappers/MapFeeLoanType.cs
          Division: division, // could be HELOC, Conventional, Government. https://git.rockfin.com/Einstein/einstein-hub-v2/blob/3a2efcf6f7a3989fbef728d747e1e9138ed6aecb/EinsteinHub.Defaults/Mappings/Fees/FeeMappers/MapFeeDivision.cs

          PropertyZip: zipCode,

          // @TODO need to get these from the form
          PropertyCountyFIPS: productsResponse.data.Property.Address.County.FIPS,
          PropertyCountyName: productsResponse.data.Property.Address.County.Name,
          PropertyState: productsResponse.data.Property.Address.State,

          PropertyType: feesCalcPropertyType,
          PropertyOccupancy: feesCalcOccupancyType,
          LoanAmount: desiredLoanAmount,
          RequestType: 'All',
          LTV: pricingOption.LtvInformation.Rounded,
          CLTV: pricingOption.LtvInformation.CLTV,

          ProductCode: product.ProductCode,
          AmortizationTerm: product.TermMonths,
          NumberOfClients: 1,
          PerDiem: pricingOption.FundsToClose.Details.NonFinancedAmounts.Details.NetPerDiem.Total,

          RequestTitle: true,
          RequestAppraisal: true,
          IsFirstTimeHomebuyer: true,
          IsIntegratedDisclore: true,
          IsDecliningMarking: false,
          IsTX50a6Refi: false,
          IsTX50f2Refi: false,
          IsCorrespondent: false,
        };

        const purchasePrice = pricingOption.FundsToClose.Details.FinalLoanAmount.Details.BaseLoanAmount.PurchaseDetails?.PurchasePrice;

        const feeFactors = loanPurpose === LoanPurpose.Purchase
          ? {...baseFeeFactors, PurchasePrice: purchasePrice}
          : {...baseFeeFactors, EstimatedMarketValue: estimatedPropertyValue};

        const request = {
          FeeFactors: feeFactors,
        };

        const source$ = environment.estimator?.useMockData
          ? of(mockFeesResponse)
          : this.httpClient.post<FeesCalcResponse>(`${this.BASE_URL}/estimator/fees`, request);

        return source$.pipe(
          map(res => {
            return res.FeeDetail
              .filter(fee => !this.feeNumbersToExclude.has(fee.FeeNumber) && fee.FeeAmount > 0)
              .map(fee => ({
                name: fee.FeeDescription,
                value: fee.FeeAmount,
              }));
          }),
        );
      }),
    );
  }

  buildSubjectProperty(input: EstimateRequest, loanPurpose: string) {
    const ehOccupancyType = (() => {
      switch (input.occupancyType) {
        case OccupancyType.PrimaryResidence: return 'Primary Residence';
        case OccupancyType.SecondHome: return 'Second';
        case OccupancyType.Investment: return 'Investment';
        default: return null;
      }
    })();

    const baseSubjectProperty = {
      type: input.propertyType,
      address: {
        zip: input.zipCode,
        state: input.state,
        county: {
          name: input.countyName
        }
      },
      occupancyType: ehOccupancyType,
      IsTexas50a6: false,
      NumberOfUnits: this.calcForm.controls.propertyType.value === PropertyType.TwoToFourFamily ? 2 : 1,
    };

    if (this.isPurchaseRequest(input)) {
      return {
        ...baseSubjectProperty,
        propertyValue: input.purchasePrice
      }
    } else if (loanPurpose === LoanPurpose.Refinance && input.isCashout) {
      return {
        ...baseSubjectProperty,
        propertyValue: input.estimatedPropertyValue,
        liens: input.currentLoanAmount ? [
          {
            LienType: "Mortgage",
            Position: 1,
            CurrentBalance: input.currentLoanAmount,
            PayoffLien: true,
            RemainingTermMonths: 0,
            AgencyType: "None"
          }
        ] : null
      }
    } else {
      return {
        ...baseSubjectProperty,
        propertyValue: input.estimatedPropertyValue,
        liens: [
          {
            LienType: "Mortgage",
            Position: 1,
            CurrentBalance: input.currentLoanAmount,
            PayoffLien: true,
            RemainingTermMonths: 0,
            AgencyType: "None"
          }
        ]
      }
    }
  }
  
  buildRequestConfiguration(input: EstimateRequest, loanPurpose: string, teamMemberRoles: TeamMemberRoles) {
    const baseRequestConfiguration = {
      LeadTypeCode: teamMemberRoles.isSchwabBanker ? 'ACCADILLAC' : 'FLOMAN',
      includeInsuranceEscrowAccount: !input.isEscrowWaived,
      includeTaxEscrowAccount: !input.isEscrowWaived,
      ApplicableIncentiveIDs: teamMemberRoles.isSchwabBanker ? ["SchwabTargetMarketOffer"] : [],
      OnlyEstimateTaxes: false,
      IncludeUFMIPInLoanAmount: false,
      ApplyLenderPaidCredits: false,
    };
    if (this.isPurchaseRequest(input)) {
      const purchasePrice = input.purchasePrice;
      const downPaymentAmount = input.downPaymentAmount;
      const downPaymentPercentage = input.downPaymentPercentage ? input.downPaymentPercentage : downPaymentAmount / purchasePrice * 100;
      return {
        ...baseRequestConfiguration,
        downPaymentPercentages: [downPaymentPercentage],
        preStructuredLoan: {
          structuredBaseLoanAmount: purchasePrice - (downPaymentPercentage / 100) * purchasePrice,
        },
      }
    } else if (loanPurpose === LoanPurpose.Refinance && input.isCashout) {
        return {
          ...baseRequestConfiguration,
          AllowCashoutRecommendation: false,
          IncludePointsInLoanAmount: false,
          IncludeAPRFeesInLoanAmount: false,
          IncludeNonAPRFeesInLoanAmount: false,
          IncludePrepaidEscrowsInLoanAmount: false,
          AllowForceSubordinationOfLiens: false,
          IsTexas50f2: false,
          EstimatePayoff: false,
      }
    } else {
        return {
          ...baseRequestConfiguration,
          IncludePointsInLoanAmount: false,
          IncludeAPRFeesInLoanAmount: false,
          IncludeNonAPRFeesInLoanAmount: false,
          IncludePrepaidEscrowsInLoanAmount: false,
          AllowForceSubordinationOfLiens: false,
          IsTexas50f2: false,
        };
    }
  }

  getProductFees(parentEligibilityGroups: ParentEligibilityGroup[], ftdDetails: FtdDetails, teamMemberRoles: TeamMemberRoles): ProductFees {
    const refinanceFees = ftdDetails.FinalLoanAmount.Details.BaseLoanAmount.RefinanceDetails?.FinancedNetFees.Details.APRFees ?? [];
    const purchaseFees = ftdDetails.NonFinancedAmounts.Details.NetFees.Details.APRFees ?? [];
    const aprFees = this.loanPurposeControl.value === LoanPurpose.Purchase
      ? purchaseFees
      : refinanceFees;

    const isFHAProduct = parentEligibilityGroups.includes(ParentEligibilityGroup.FHA);
    const isVAProduct = parentEligibilityGroups.includes(ParentEligibilityGroup.VA);

    const isCashoutValue = this.calcForm.controls.isCashout.value;
    const desiredCashoutAmountValue = this.calcForm.controls.desiredCashoutAmount.value;
    const currentLoanAmountValue = this.calcForm.controls.currentLoanAmount.value;

    const refinanceLoanAmount = this.loanPurposeControl.value === LoanPurpose.Refinance && isCashoutValue ? desiredCashoutAmountValue : currentLoanAmountValue;
    const loanAmount = this.loanPurposeControl.value === LoanPurpose.Purchase
      ? this.calcForm.controls.loanAmount.value : refinanceLoanAmount;
    
    const defaultProcessingFee = aprFees.find((fee) => fee?.Code === '810') ?? null;
    const defaultOriginationFee = aprFees.find((fee) => fee?.Code === '801') ?? null;
    const defaultLoanDiscountFee = aprFees.find((fee) => fee?.Code === '802') ?? null;
    
    if (!loanAmount) {
      const defaultProductFees: ProductFees = {
        loanDiscountFee: defaultLoanDiscountFee,
        originationFee: defaultOriginationFee,
        processingFee: defaultProcessingFee,
      };
      return defaultProductFees;
    }
    const onePercentOfLoanAmount = (loanAmount * 1) / 100;
    const franchiseFHAOriginationFeeAmount = Math.min(2500, onePercentOfLoanAmount);
    
    const schwabOriginationFee = isFHAProduct || isVAProduct ? null 
      : {
        Name: 'Loan Origination',
        Code: '801',
        Amount: 1195,
      } satisfies AprFee;

    const franchiseFHAOriginationFee = {
      Name: 'Loan Origination',
      Code: '801',
      Amount: franchiseFHAOriginationFeeAmount,
    } satisfies AprFee;

    const franchiseProcessingFee = isFHAProduct || isVAProduct ? defaultProcessingFee
      : {
        Name: 'Processing Fee',
        Code: '802',
        Amount: 900,
      } satisfies AprFee;

    const isSchwabProduct = parentEligibilityGroups.includes(ParentEligibilityGroup.Schwab);

    const processingFee = teamMemberRoles.isFranchiseBanker ? franchiseProcessingFee : defaultProcessingFee;
    const franchiseOriginationFee = isFHAProduct && teamMemberRoles.isFranchiseBanker ? franchiseFHAOriginationFee : defaultOriginationFee;

    const originationFee = isSchwabProduct
      ? schwabOriginationFee
      : teamMemberRoles.isFranchiseBanker ? franchiseOriginationFee : defaultOriginationFee;

    const productFees: ProductFees = {
      loanDiscountFee: defaultLoanDiscountFee,
      originationFee: originationFee,
      processingFee: isSchwabProduct ? null : processingFee,
    };  
    return productFees;
  }

  getTotalFees(productFees: AprFee[], closingFees?: Fee[]): number {
    const productFeesTotal = productFees.reduce((acc, fee) => acc + (fee?.Amount ?? 0), 0) ?? 0;
    const closingFeesTotal = closingFees?.reduce((acc, fee) => acc + fee.value, 0) ?? 0;
    return productFeesTotal + closingFeesTotal;
  }

  async getClosingFees(product: Product, pricingOption: PricingOption): Promise<Fee[]> {
    try {
      const fees = await firstValueFrom(this.getClosingFees$(product, pricingOption));
      return fees;
    } catch (error) {
      console.error('Error fetching closing fees:', error);
      throw error;
    }
  }

  navigate(toStep: EstimatorStep) {
    this.activePageSubject.next(toStep);
  } 

  getEstimateDetails$(opportunityId: string): Observable<EstimateDetails> {
    return this.httpClient.get<EstimateDetails>(`${this.BASE_URL}/estimator/estimates/${opportunityId}`);
  }

  saveEstimateDetails$(opportunityId: string): Observable<void> {
    return this.checkedPricingOptions$.pipe(
      take(1),
      switchMap(checkedOptions => {
        const productCodes = [...new Set(checkedOptions.map(option => option.product.ProductCode))];
        const estimateDetails = {
          calcInputs: Object.fromEntries(
            Object.entries(this.calcForm.value).filter(([_, value]) => value !== null && value !== undefined)
          ),
          loanPurpose: this.loanPurposeControl.value,
          selectedProductCodes: productCodes,
          createdAt: new Date().toISOString()
        };
        return this.httpClient.post<void>(`${this.BASE_URL}/estimator/estimates/${opportunityId}`, estimateDetails);
      })
    )
  }

  async saveEstimateDetails(opportunityId: string): Promise<void> {
    try {
      return await firstValueFrom(this.saveEstimateDetails$(opportunityId));
    } catch (error) {
      console.error('Error saving estimate details:', error);
      throw error;
    }
  }

  async getEstimateDetails(opportunityId: string): Promise<EstimateDetails> {
    try {
      const estimateDetails = await firstValueFrom(this.getEstimateDetails$(opportunityId));
      return estimateDetails;
    } catch (error) {
      console.error('Error fetching estimate details:', error);
      throw error;
    }
  }

  modifyEstimateDetails() {
    // unlock the calculator and keep current estimate details
    this.loanPurposeControl.enable({emitEvent: false});
    this.calcForm.enable({emitEvent: false});
  }

  clearEstimateDetails() {
    // clear the current estimate details and unlock the calculator
    this.loanPurposeControl.enable({emitEvent: false});
    this.calcForm.enable({emitEvent: false});
    this.router.navigate(['estimator']);
    this.reset();
  }
}
