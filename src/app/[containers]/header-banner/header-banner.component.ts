import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { AuthService } from '@auth0/auth0-angular';
import { map } from 'rxjs';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { SubjectPropertyStateService } from '../../services/entity-state/subject-property-state/subject-property-state.service';
import { AddressPipe } from '../../util/address.pipe';
import { ClientNamePipe } from '../../util/client-name.pipe';

@Component({
  selector: 'app-header-banner',
  standalone: true,
  imports: [ClientNamePipe, AddressPipe],
  templateUrl: './header-banner.component.html',
  styleUrl: './header-banner.component.scss',
})
export class HeaderBannerComponent {
  readonly authService = inject(AuthService);
  protected readonly clientStateService = inject(ClientStateService);
  protected readonly subjectPropStateService = inject(SubjectPropertyStateService);

  readonly userProfilePicture = toSignal(this.authService.user$.pipe(map((user) => user?.picture)));

  readonly primaryClient = computed(() =>
    this.clientStateService.stateValues().find((client) => client.isPrimaryBorrower),
  );
  readonly subjectProperty = computed(() => this.subjectPropStateService.state()?.data);
}
