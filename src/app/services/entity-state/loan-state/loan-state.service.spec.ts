import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LeadService } from '../../lead/lead.service';
import { LoanApplicationStateService } from '../../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { LoanStatusLoadingService } from '../../loan-status-loading/loan-status-loading.service';
import { LoanStateService } from './loan-state.service';
describe('LoanStateService', () => {
  let service: LoanStateService;

  beforeEach(() =>
    MockBuilder(LoanStateService)
      .mock(LoanIdService, { loanId$: NEVER })
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(LoanApplicationStateService, { isXpCompleted$: NEVER })
      .mock(LeadService, { isLeadRlbCompleted$: NEVER })
      .mock(LoanStatusLoadingService, { loanLoadingError$: NEVER })
      .mock(UserAuthorizationService, { isLoanArchived$: NEVER }),
  );
  beforeEach(() => {
    service = TestBed.inject(LoanStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
