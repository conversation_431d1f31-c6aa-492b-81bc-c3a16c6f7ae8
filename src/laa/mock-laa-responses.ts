

/**
 * Simplified mock LAA responses - all loans return as archived
 */

// Mock response for archived loan (V3 format)
export const mockArchivedLoanResponse = {
  isUserAuthorized: true,
  applicationAuth: {
    LoanIsArchived: {
      exclusion: {
        reason: 'LoanIsArchived',
      },
    },
    'rl-xp': {
      exclusions: [
        {
          reason: 'InAmpArchive',
        },
      ],
    },
  },
};
