

import { LoanAccessDecision } from 'src/models/authorization/access-control-list';

/**
 * Mock LAA responses for local development and testing
 */

// Custom interface for LAA response that matches what archived loan service expects
interface ArchivedLoanLaaResponse {
  isUserAuthorized: boolean;
  applicationAuth: {
    LoanIsArchived?: { exclusion?: { reason?: string } };
    'rl-xp'?: { exclusions?: Array<{ reason?: string }> };
  };
}

// Mock response for archived loan
export const mockArchivedLoanResponse: ArchivedLoanLaaResponse = {
  isUserAuthorized: true,
  applicationAuth: {
    LoanIsArchived: {
      exclusion: {
        reason: 'LoanIsArchived',
      },
    },
    'rl-xp': {
      exclusions: [
        {
          reason: 'InAmpArchive',
        },
      ],
    },
  },
};

// Mock response for non-archived loan
export const mockNonArchivedLoanResponse: ArchivedLoanLaaResponse = {
  isUserAuthorized: true,
  applicationAuth: {
    LoanIsArchived: {
      // No exclusion means loan is not archived
    },
    'rl-xp': {
      // No exclusions means loan is accessible
      exclusions: [],
    },
  },
};

// Mock responses for LoanAccessDecision format (used by getLoanAccess method)
export const mockArchivedLoanAccessDecision: LoanAccessDecision = {
  IsBankerLicensed: true,
  RlXpDenyWithdrawVerifier: false,
  LoanIsArchived: true, // This indicates the loan is archived
  'rl-xp': false, // This indicates access is denied due to archival
};

export const mockNonArchivedLoanAccessDecision: LoanAccessDecision = {
  IsBankerLicensed: true,
  RlXpDenyWithdrawVerifier: false,
  LoanIsArchived: false, // This indicates the loan is not archived
  'rl-xp': true, // This indicates access is allowed
};

// Mock loan IDs that should return archived responses (must be 10-digit numbers)
export const mockArchivedLoanIds = [
  '**********', // Example archived loan ID
  '**********', // Archived loan ending with 999
  '**********', // Archived loan ending with 000
  '**********', // Another archived loan ending with 999
  '**********', // Another archived loan ending with 000
  '**********', // Specific archived loan ID
];

/**
 * Determines if a loan ID should return an archived response
 * @param loanId The loan identifier (should be 10-digit number)
 * @returns true if the loan should be treated as archived
 */
export function shouldReturnArchivedResponse(loanId: string): boolean {
  // Check if loan ID is in the mock archived list
  if (mockArchivedLoanIds.includes(loanId)) {
    return true;
  }

  // For 10-digit loan numbers, check if they end with specific patterns that indicate archived loans
  if (loanId.length === 10 && /^\d{10}$/.test(loanId)) {
    // Check if loan ID ends with 999 or 000 (archived patterns)
    const archivedPatterns = ['999', '000'];
    return archivedPatterns.some((pattern) => loanId.endsWith(pattern));
  }

  // Fallback: if not a 10-digit number, still check for 'archived' in the ID (for testing)
  if (loanId.toLowerCase().includes('archived')) {
    return true;
  }

  return false;
}
