import { UserAuthorizationResponse } from 'src/models/authorization/access-control-list';

/**
 * Mock LAA responses for local development and testing
 */

// Mock response for archived loan
export const mockArchivedLoanResponse: UserAuthorizationResponse = {
  isUserAuthorized: true,
  applicationAuth: {
    LoanIsArchived: {
      exclusion: {
        reason: 'LoanIsArchived',
        message: 'This loan is archived and cannot be edited'
      }
    },
    'rl-xp': {
      exclusions: [
        {
          reason: 'InAmpArchive',
          message: 'Loan is in AMP archive'
        }
      ]
    }
  }
};

// Mock response for non-archived loan
export const mockNonArchivedLoanResponse: UserAuthorizationResponse = {
  isUserAuthorized: true,
  applicationAuth: {
    LoanIsArchived: {
      // No exclusion means loan is not archived
    },
    'rl-xp': {
      // No exclusions means loan is accessible
      exclusions: []
    }
  }
};

// Mock loan IDs that should return archived responses
export const mockArchivedLoanIds = [
  '123456789',  // Example archived loan ID
  'ARCHIVED001',
  'ARCHIVED002',
  'TEST-ARCHIVED'
];

/**
 * Determines if a loan ID should return an archived response
 * @param loanId The loan identifier
 * @returns true if the loan should be treated as archived
 */
export function shouldReturnArchivedResponse(loanId: string): boolean {
  // Check if loan ID is in the mock archived list
  if (mockArchivedLoanIds.includes(loanId)) {
    return true;
  }
  
  // Check if loan ID contains 'archived' (case insensitive)
  if (loanId.toLowerCase().includes('archived')) {
    return true;
  }
  
  // Check if loan ID ends with specific patterns that indicate archived loans
  const archivedPatterns = ['999', '000', 'arch'];
  return archivedPatterns.some(pattern => loanId.toLowerCase().endsWith(pattern));
}
