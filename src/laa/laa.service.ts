import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Observable, catchError, map, of } from 'rxjs';
import { HttpErrorHandler } from 'src/http/http-error-handler';
import { ConsoleLoggerService } from 'src/logger/implementations/console-logger.service';
import { CreateManualExclusionRequest } from 'src/models/authorization';
import {
  LoanAccessDecision,
  LoanAccessDecisionV4,
  UserAccessControlList,
  UserAuthorizationResponse,
} from 'src/models/authorization/access-control-list';
import { OauthClientService } from 'src/oauth/oauth-client/oauth-client.service';
import { UserInfo } from 'src/request-context/request-context.service';
import {
  mockArchivedLoanResponse,
  mockNonArchivedLoanResponse,
  shouldReturnArchivedResponse,
} from './mock-laa-responses';

@Injectable()
export class LaaService {
  constructor(
    private oauthClient: OauthClientService,
    private logger: ConsoleLoggerService,
    private httpErrorHandler: HttpErrorHandler,
    private configService: ConfigService,
  ) {}

  /**
   * Check if we should use mock LAA responses for local development
   */
  private shouldUseMockData(): boolean {
    const appEnvironment = this.configService.get<string>('appEnvironment');
    const useMockLaa = this.configService.get<boolean>('Features.useMockLaa');
    const laaUseMockData = this.configService.get<boolean>('LAA_USE_MOCK_DATA');

    // Use mock data in local environment or when explicitly enabled
    return appEnvironment === 'local' || useMockLaa === true || laaUseMockData === true;
  }

  /**
   * Get mock LAA response based on loan ID and pilot IDs
   */
  private getMockLaaResponse(loanNumber: string, pilotIds: string[]): UserAuthorizationResponse {
    // Check if this request is for archived loan checking
    const isArchiveCheck = pilotIds.includes('LoanIsArchived') || pilotIds.includes('rl-xp');

    if (isArchiveCheck && shouldReturnArchivedResponse(loanNumber)) {
      this.logger.debug(`Returning mock archived response for loan ${loanNumber}`);
      return mockArchivedLoanResponse;
    }

    this.logger.debug(`Returning mock non-archived response for loan ${loanNumber}`);
    return mockNonArchivedLoanResponse;
  }

  public getUserAccess(
    loanNumber: string,
    pilotIds: string[],
    context: UserInfo,
  ): Observable<UserAccessControlList> {
    return this.oauthClient
      .post<UserAccessControlList>(`loan/${loanNumber}/user-access`, pilotIds, {
        headers: {
          'x-common-id': context.commonId,
          'x-amp-id': context.unixId,
          'x-request-source': 'rl-xp-bff',
        },
      })
      .pipe(
        map((response) => response.data),
        catchError((err) =>
          this.httpErrorHandler.handle<UserAccessControlList>(
            err,
            `An error occurred attempting to get access decisions ${loanNumber}`,
          ),
        ),
      );
  }

  public getV3UserAccess(loanNumber: string, pilotIds: string[], context: UserInfo) {
    // Check if we should use mock data for local development
    if (this.shouldUseMockData()) {
      this.logger.debug(`Using mock LAA response for loan ${loanNumber} with pilots: ${pilotIds.join(', ')}`);
      const mockResponse = this.getMockLaaResponse(loanNumber, pilotIds);
      return of(mockResponse);
    }

    return this.oauthClient
      .post<UserAuthorizationResponse>(`loan/${loanNumber}/user-access`, pilotIds, {
        headers: {
          'x-common-id': context.commonId,
          'x-amp-id': context.unixId,
          'x-request-source': 'rl-xp-bff',
          'x-api-version': 3,
        },
      })
      .pipe(
        map((response) => response.data),
        catchError((err) =>
          this.httpErrorHandler.handle<UserAuthorizationResponse>(
            err,
            `An error occurred attempting to get access decisions ${loanNumber}`,
          ),
        ),
      );
  }

  public getLoanAccess<T = LoanAccessDecision>(
    loanNumber: string,
    pilotIds: string[],
    context: UserInfo,
    apiVersion = 3,
  ): Observable<T> {
    return this.oauthClient
      .post<T>(`loan/${loanNumber}/application/access`, pilotIds, {
        headers: {
          'x-request-source': 'rl-xp-bff',
          'x-common-id': context.commonId,
          'x-amp-id': context.unixId,
          'x-api-version': apiVersion,
        },
      })
      .pipe(
        map((response) => response.data),
        catchError((err) =>
          this.httpErrorHandler.handle<T>(
            err,
            `An error occurred attempting to get loan access for loan ${loanNumber}`,
          ),
        ),
      );
  }

  public getLoanAccessV4(
    loanNumber: string,
    pilotIds: string[],
    context: UserInfo,
  ): Observable<LoanAccessDecisionV4> {
    return this.getLoanAccess<LoanAccessDecisionV4>(loanNumber, pilotIds, context, 4);
  }

  public createExclusion(
    loanNumber: string,
    exclusion: CreateManualExclusionRequest,
    context: UserInfo,
  ): Observable<void> {
    return this.oauthClient
      .post<void>(`exclusion/loan/${loanNumber}`, exclusion, {
        headers: {
          'x-request-source': 'rl-xp-bff',
          'x-common-id': context.commonId,
          'x-amp-id': context.unixId,
        },
      })
      .pipe(
        map((response) => response.data),
        catchError((err) =>
          this.httpErrorHandler.handle<void>(
            err,
            `An error occurred attempting to create exclusion for loan ${loanNumber}`,
          ),
        ),
      );
  }
}
