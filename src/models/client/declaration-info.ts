import {
    // BankruptcyDeclaration as RLBankruptcy, // This export doesn't exist
    DeclarationInformation as RLDeclarationInfo,
    Foreclosure as RLForeclosure,
    Forfeiture as RLForfeiture,
    Mortgage as RLMortgage,
    PreForeclosureOrShortSale as RLPreForeclosureOrShortSale,
} from '@rocket-logic/rocket-logic-api-models/client';
import { Address } from '../address';

export {
    BankruptcyInformation,
    BankruptcyStatus,
    BorrowedFund,
    BorrowedFundsInformation,
    CitizenshipResidencyType,
    ClientOccupancyType,
    ClientSellerRelationship,
    CreditApplication,
    CreditApplicationInformation,
    FamilyRelationshipType,
    ForeclosureInformation,
    ForfeitureInformation,
    Lawsuit,
    LawsuitCompletionTimeframe,
    LawsuitInformation,
    LawsuitPartyType,
    LawsuitStatus,
    MortgageInformation,
    PaymentPlan,
    PreForeclosureOrShortSaleInformation,
    PropertyInformation,
    PropertyTitleHeldByType,
    SellerRelationshipInformation
} from '@rocket-logic/rocket-logic-api-models/client';

// export { BankruptcyType } from '@rocket-logic/rocket-logic-api-models/shared/enums/bankruptcy-type'; // Module doesn't exist

export interface Bankruptcy {
  propertyAddress?: Address;
  // Add other bankruptcy properties as needed
}

export interface Mortgage extends Omit<RLMortgage, 'address'> {
  address: Address;
}

export interface PreForeclosureOrShortSale
  extends Omit<RLPreForeclosureOrShortSale, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface Forfeiture extends Omit<RLForfeiture, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface Foreclosure extends Omit<RLForeclosure, 'propertyAddress'> {
  propertyAddress?: Address;
}

export interface DeclarationInformation
  extends Omit<
    RLDeclarationInfo,
    | 'bankruptcyInformation'
    | 'mortgageInformation'
    | 'preForeclosureOrShortSaleInformation'
    | 'forfeitureInformation'
    | 'foreclosureInformation'
  > {
  bankruptcyInformation?: {
    bankruptcies?: Bankruptcy[];
  };
  mortgageInformation?: {
    otherMortgages?: Mortgage[];
  };
  preForeclosureOrShortSaleInformation?: {
    preForeclosureOrShortSales?: PreForeclosureOrShortSale[];
  };
  forfeitureInformation?: {
    forfeitures?: Forfeiture[];
  };
  foreclosureInformation?: {
    foreclosures?: Foreclosure[];
  };
}
