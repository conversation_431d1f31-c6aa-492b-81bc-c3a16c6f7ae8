import {
  BuydownFundSourceType,
  Incentive,
  IneligibilityReasonDetails,
  LenderPaidBuydownOptOutReason,
  ManualAdjustmentBounds,
  ProductArmInformationBase,
  QualificationGroupDetails,
  QualifiedAmounts,
} from '@rocket-logic/rocket-logic-api-models/solutions';
import { BuydownDuration } from './buydown-duration';
import { ClosingDisclosure } from './closing-disclosure';
import {
  AmortizationType,
  MortgageInsuranceType,
  MortgageType,
  PaymentInformation,
  PricingAdjustment,
} from './product-qualification';
import { ProductType } from './product-type';
import { ShortageInformation } from './shortage-information';

export interface Product {
  productGroup?: MortgageType;
  productType: ProductType;
  amortizationType?: AmortizationType;
  productCode: string;
  rate: number;
  baseRate: number;
  term: number;
  frontendDebtToIncome: number;
  backendDebtToIncome: number;
  qualificationGroupDetails: QualificationGroupDetails;
  mortgageInsuranceType?: MortgageInsuranceType;
  baseLoanAmount?: number;
  collectedDiscountPoints?: number;
  buydown?: BuydownDuration;
  buydownSources?: BuydownFundSourceType[];
  lenderPaidBuydownNotAllowedReasons?: string[];
  lenderPaidBuydownOptOutReason?: LenderPaidBuydownOptOutReason;
  closingCostDetailSummary?: ClosingDisclosure;
  credits?: PricingCredits;
  closingDetails: ClosingDetails;
  qualificationDetails?: QualificationDetails;
  paymentInformation?: PaymentInformation;
  productDescription: string;
  armInformation?: ProductArmInformationBase;
  ineligibilities?: IneligibilityReasonDetails[];
  stoppingIneligibilities?: IneligibilityReasonDetails[];
  adjustedSalesAmount?: number;
  lastPricingUpdate?: string;
  regulatoryFindingsId?: string;
  shortageInformation?: ShortageInformation;
  incentives?: Incentive[];
  loanApplicationReceivedDate?: string;
  loanEstimateSentDate?: string;
}

export interface QualificationDetails {
  totalLoanAmount?: number;
  totalMonthlyPayment?: number;
  collectedDiscountsAmount?: number;
  collectedDiscountPoints?: number;
  collectedDiscountPointsManualAdjustmentBounds?: ManualAdjustmentBounds;
  requiredDiscountPoints?: number;
  requiredDiscountsAmount?: number;
  maxApr?: number;
  aprCushion?: number;
  apr?: number;
  ltv?: number;
  cltv?: number;
  dti?: number;
  pricingAdjustments?: PricingAdjustment[];
  basePoints?: number;
  estimatedCashFromClient?: number;
  qualifiedAmounts?: QualifiedAmounts;
}

export interface PricingCredits {
  totalLenderPaidCredit?: number;
  manualLenderPaidCredit?: number;
  manualLenderPaidCreditAdjustmentBounds?: ManualAdjustmentBounds;
  realtorCredits?: number;
  sellerConcessions?: number;
  promotionalCredits?: number;
  maximumSellerConcessionsAllowed?: number;
}

export interface ClosingDetails {
  commitmentPeriodInDays: number;
  includeFundingFeesInLoanAmount?: boolean;
  closingCosts?: number;
  downPayment?: number;
  downPaymentPercentage?: number;
  downPaymentWithoutFee?: number;
  downPaymentWithoutFeePercentage?: number;
  lockExpirationDate?: string;
}
