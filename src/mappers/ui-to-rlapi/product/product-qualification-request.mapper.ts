import {
    ProductQualificationRequest,
    ProductQualificationShortageInformation,
} from '@rocket-logic/rocket-logic-api-models/solutions';
import { IMapper } from 'src/mappers/mapper';
import { ProductQualificationOverrides, Product as UIProduct } from 'src/models/products';

export class ProductQualificationRequestMapper implements IMapper<UIProduct, ProductQualificationRequest> {
  mapToDestination(
    source: UIProduct,
    productCode: string,
    overrides?: ProductQualificationOverrides,
  ): ProductQualificationRequest {
    return {
      productCode,
      baseRate: source.baseRate,
      baseLoanAmount: source.baseLoanAmount ?? 0,
      commitmentPeriodInDays: source.closingDetails.commitmentPeriodInDays,
      financeUpfrontMortgageInsurancePremiumAndFundingFee:
        source.closingDetails.includeFundingFeesInLoanAmount ?? false,
      mortgageInsuranceType: source.mortgageInsuranceType,
      buydownDurationInYears: source.buydown,
      buydownFundSources: source.buydownSources,
      lenderPaidBuydownOptOutReason: source.lenderPaidBuydownOptOutReason,
      shortageInformation: this.mapShortgageInfo(source),
      sellerConcessionsAmount: source.credits?.sellerConcessions,
      realtorCreditsAmount: source.credits?.realtorCredits,
      purchasePrice: overrides?.purchasePrice,
      // downPaymentAmount: overrides?.downPayment, // Property doesn't exist in ProductQualificationRequest
    };
  }

  private mapShortgageInfo(source: UIProduct): ProductQualificationShortageInformation | undefined {
    const requiredDiscountPoints = source.qualificationDetails?.requiredDiscountPoints;
    const shouldSendPointsAsShortage = this.shouldSendPointsAsShortage(
      source.collectedDiscountPoints,
      requiredDiscountPoints,
    );
    if (
      (source.credits?.manualLenderPaidCredit == null ||
        source.credits?.manualLenderPaidCredit === 0) &&
      !shouldSendPointsAsShortage
    ) {
      return;
    }

    return {
      manualLenderCreditAmount: source.credits?.manualLenderPaidCredit,
      collectedDiscountPointsOverrideAmount: shouldSendPointsAsShortage
        ? source.collectedDiscountPoints
        : undefined,
    };
  }

  private shouldSendPointsAsShortage(
    collectedDiscountPoints?: number,
    requiredDiscountPoints?: number,
  ) {
    return (
      collectedDiscountPoints != null &&
      (collectedDiscountPoints ?? 0) < (requiredDiscountPoints ?? 0)
    );
  }
}
