import { Injectable } from '@nestjs/common';
import { LoanPurpose } from '@rocket-logic/rocket-logic-api-models/loan';
import {
  ClosingCostDetailSummary,
  FundsToCloseInformation,
  ManualAdjustmentBounds,
  PurchaseFundsToCloseInformation,
  SelectedProductQualificationResponse,
} from '@rocket-logic/rocket-logic-api-models/solutions';
import { getDownPayments } from 'src/loan-amount-calc/utils/get-down-payments';
import { IMapper } from 'src/mappers/mapper';
import {
  BuydownDuration,
  ClosingDetails,
  PricingCredits,
  QualificationDetails,
  Product as UIProduct,
} from 'src/models/products';
import { ClosingDisclosure } from '../../../models/products/closing-disclosure';
import { ProductTypeMapper } from './product-type.mapper';

@Injectable()
export class UIProductMapper implements IMapper<SelectedProductQualificationResponse, UIProduct> {
  private readonly buydownDurations = [
    BuydownDuration.None,
    BuydownDuration.OneZero,
    BuydownDuration.TwoOne,
    BuydownDuration.ThreeTwoOne,
  ];

  constructor(private productTypeMapper: ProductTypeMapper) {}

  mapToDestination(
    source: SelectedProductQualificationResponse,
    sellerConcessionsAmount?: number,
  ): UIProduct {
    const buydownInformation = source.product.pricingOptions[0].buydownInformation;
    const ineligibilities = source.product.pricingOptions[0].ineligibilityReasonDetails ?? [];
    const stoppingIneligibilities = source.product.pricingOptions[0].stoppingIneligibilites ?? [];
    const frontendDebtToIncome = source.product.pricingOptions[0].frontendDebtToIncome * 100;
    const backendDebtToIncome = source.product.pricingOptions[0].backendDebtToIncome * 100;
    const shortageInformation = source.product.pricingOptions[0].shortageInformation ?? undefined;

    return {
      frontendDebtToIncome,
      backendDebtToIncome,
      buydown: this.mapBuydownDuration(buydownInformation.buydownDurationInYears),
      buydownSources: buydownInformation.buydownFundSources,
      lenderPaidBuydownNotAllowedReasons: buydownInformation.lenderPaidBuydownNotAllowedReasons,
      lenderPaidBuydownOptOutReason: buydownInformation.lenderPaidBuydownOptOutReason,
      productGroup: source.product.qualificationGroupDetails.mortgageType,
      productType: this.productTypeMapper.mapToDestination(
        source.product.qualificationGroupDetails,
      ),
      productCode: source.product.productCode,
      amortizationType: source.product.qualificationGroupDetails.amortizationType,
      rate: source.product.pricingOptions[0].interestRateDetails.adjustedRate,
      baseRate: source.product.pricingOptions[0].interestRateDetails.baseRate,
      term: source.product.termInMonths / 12,
      mortgageInsuranceType: source.product.mortgageInsuranceType,
      baseLoanAmount: source.product.pricingOptions[0].fundsToCloseInformation.baseLoanAmount,
      closingCostDetailSummary: this.mapClosingDisclosure(
        source.product.pricingOptions[0].closingCostDetailSummary,
      ),
      collectedDiscountPoints:
        source.product.pricingOptions[0].discountPointDetails.collectedDiscountPoints,
      credits: this.mapPricingCredits(source, sellerConcessionsAmount),
      closingDetails: this.mapClosingDetails(source),
      qualificationDetails: this.mapQualificationDetails(source),
      paymentInformation: source.product.pricingOptions[0].paymentInformation,
      productDescription: source.product.productDescription,
      armInformation: source.product.productArmInformation,
      qualificationGroupDetails: source.product.qualificationGroupDetails,
      ineligibilities: ineligibilities,
      stoppingIneligibilities: stoppingIneligibilities,
      adjustedSalesAmount: source.product.pricingOptions[0].adjustedSalesAmount,
      lastPricingUpdate: source.lastPricingUpdate,
      regulatoryFindingsId: source.product.pricingOptions[0].regulatoryFindingsId,
      shortageInformation: shortageInformation,
      incentives: source.product.pricingOptions[0].incentives,
      loanApplicationReceivedDate: source.scheduleOfDates?.loanApplicationReceivedDate ?? undefined,
      loanEstimateSentDate: source.scheduleOfDates?.loanEstimateSentDate ?? undefined,
    };
  }

  private mapPricingCredits(
    source: SelectedProductQualificationResponse,
    sellerConcessionsAmount?: number,
  ): PricingCredits {
    return {
      totalLenderPaidCredit:
        source.product.pricingOptions[0].fundsToCloseInformation.totalLenderPaidCredit,
      manualLenderPaidCredit:
        source.product.pricingOptions[0].fundsToCloseInformation.manualLenderPaidCreditAmount,
      manualLenderPaidCreditAdjustmentBounds: this.mapManualLenderPaidCreditBounds(source),
      realtorCredits: source.realtorCreditsAmount ?? undefined,
      sellerConcessions: source.sellerConcessions ?? sellerConcessionsAmount,
      maximumSellerConcessionsAllowed:
        source.product.pricingOptions[0].maximumSellerConcessionsAllowed,
      // promotionalCredits: source.product.pricingOptions[0].pricingCredits.promotionalCredits, // TODO not currently returned
    };
  }

  private mapManualLenderPaidCreditBounds(
    source: SelectedProductQualificationResponse,
  ): ManualAdjustmentBounds {
    return {
      ceiling:
        source.product.pricingOptions[0].fundsToCloseInformation.manualLenderPaidCreditMaximum,
      floor: source.product.pricingOptions[0].fundsToCloseInformation.manualLenderPaidCreditMinimum,
      canIncrease: true,
      canDecrease: true,
    };
  }

  private mapClosingDetails(source: SelectedProductQualificationResponse): ClosingDetails {
    const pricingOption = source.product.pricingOptions[0];
    const downPaymentInfoWithoutFee = this.mapDownPaymentWithoutFee(
      pricingOption.fundsToCloseInformation,
    );

    return {
      commitmentPeriodInDays: pricingOption.commitmentPeriodInDays,
      closingCosts: pricingOption.closingCostDetailSummary.totalClientPaidClosingCosts,
      downPayment: pricingOption.fundsToCloseInformation.downPayment,
      downPaymentPercentage: this.mapDownPaymentPercentage(pricingOption.fundsToCloseInformation),
      downPaymentWithoutFee: downPaymentInfoWithoutFee?.amount,
      downPaymentWithoutFeePercentage: downPaymentInfoWithoutFee?.percent,
      lockExpirationDate: source.product.pricingOptions[0].lockExpirationDate,
      includeFundingFeesInLoanAmount: source.financeUpFrontMortgageInsurancePremiumAndFundingFee,
    };
  }

  private mapQualificationDetails(
    source: SelectedProductQualificationResponse,
  ): QualificationDetails {
    const qualificationDetails: QualificationDetails = {
      totalLoanAmount: source.product.pricingOptions[0].fundsToCloseInformation.totalLoanAmount,
      totalMonthlyPayment: source.product.pricingOptions[0].paymentInformation.totalPayment,
      apr: source.product.pricingOptions[0].annualPercentageRate,
      maxApr: source.product.pricingOptions[0].annualPercentageRate, // Using annualPercentageRate as maxAnnualPercentageRate doesn't exist
      ltv: source.product.pricingOptions[0].loanToValueDetails.loanToValue * 100,
      cltv: source.product.pricingOptions[0].loanToValueDetails.combinedLoanToValue * 100,
      dti: source.product.pricingOptions[0].debtToIncomeRatio * 100,
      requiredDiscountPoints:
        source.product.pricingOptions[0].discountPointDetails.requiredDiscountPoints,
      collectedDiscountPointsManualAdjustmentBounds:
        source.product.pricingOptions[0].discountPointDetails
          .collectedDiscountPointsManualAdjustmentBounds,
      collectedDiscountPoints:
        source.product.pricingOptions[0].discountPointDetails.collectedDiscountPoints,
      pricingAdjustments: source.product.pricingOptions[0].discountPointDetails.pricingAdjustments,
      basePoints:
        source.product.pricingOptions[0].discountPointDetails.baseDiscountPointsBeforeAdjustments,
      estimatedCashFromClient:
        source.product.pricingOptions[0].cashToCloseInformation.totalAmountRequiredAtClosing,
      qualifiedAmounts: source.product.pricingOptions[0].qualifiedAmounts,
    };

    qualificationDetails.aprCushion = this.getAprCushion(
      source.product.pricingOptions[0].annualPercentageRate, // Using annualPercentageRate as maxAnnualPercentageRate doesn't exist
      source.product.pricingOptions[0].annualPercentageRate,
    );

    qualificationDetails.collectedDiscountsAmount = this.getDiscountsAmount(
      source.product.pricingOptions[0].discountPointDetails.collectedDiscountPoints,
      qualificationDetails.totalLoanAmount,
    );

    qualificationDetails.requiredDiscountsAmount = this.getDiscountsAmount(
      qualificationDetails.requiredDiscountPoints,
      qualificationDetails.totalLoanAmount,
    );

    return qualificationDetails;
  }

  private getAprCushion(maxApr?: number, currApr?: number): number | undefined {
    if (maxApr != null && currApr != null) {
      return Math.round((maxApr - currApr) * 1000) / 1000;
    }

    return undefined;
  }

  private getDiscountsAmount(points?: number, totalLoanAmount?: number): number | undefined {
    if (points != null && totalLoanAmount != null) {
      return points * 0.01 * totalLoanAmount;
    }
    return undefined;
  }

  private mapDownPaymentPercentage(
    fundsToCloseInformation: FundsToCloseInformation,
  ): number | undefined {
    const isPurchase = fundsToCloseInformation.loanPurpose === LoanPurpose.Purchase;
    if (isPurchase) {
      const purchaseFundsToClose = fundsToCloseInformation as PurchaseFundsToCloseInformation;
      return purchaseFundsToClose.downPaymentPercentage * 100;
    }
  }

  private mapBuydownDuration(source?: number): BuydownDuration {
    const durationInYears = source ?? 0;
    if (durationInYears > this.buydownDurations.length) {
      throw new Error(`Invalid buydown duration: ${durationInYears}`);
    }
    return this.buydownDurations[durationInYears];
  }

  private mapDownPaymentWithoutFee(
    fundsToCloseInformation: FundsToCloseInformation,
  ): { amount: number; percent: number } | undefined {
    if (
      this.isPurchaseFundsToClose(fundsToCloseInformation) &&
      fundsToCloseInformation.totalLoanAmount != null
    ) {
      return getDownPayments(
        fundsToCloseInformation.purchasePrice,
        fundsToCloseInformation.totalLoanAmount,
      );
    }
  }

  private mapClosingDisclosure(source: ClosingCostDetailSummary): ClosingDisclosure {
    return {
      groupA: source.originationCharges,
      groupB: source.servicesClientCannotShopFor,
      groupC: source.servicesClientCanShopFor,
      groupE: source.taxesAndOtherGovernmentFees,
      groupF: source.prepaids,
      groupG: source.initialEscrowPaymentAtClosing,
      groupH: source.other,
      adjustmentCreditsAndCharges: source.adjustmentCreditsAndCharges,
      totalDownPayment: source.totalDownPayment,
      totalClientPaidClosingCosts: source.totalClientPaidClosingCosts,
      totalEarnestMoneyDeposit: source.totalEarnestMoneyDeposit,
      totalClientPaidOutsideClosingCosts: source.totalClientPaidOutsideClosingCosts,
      totalSellerConcessions: source.totalSellerConcessions,
      totalClientPaidOtherCosts: source.totalClientPaidOtherCosts,
      totalFinancedClosingCosts: source.totalFinancedClosingCosts,
      totalLoanCosts: source.totalLoanCosts,
    };
  }

  private isPurchaseFundsToClose(
    fundsToCloseInformation: FundsToCloseInformation,
  ): fundsToCloseInformation is PurchaseFundsToCloseInformation {
    return fundsToCloseInformation.loanPurpose === LoanPurpose.Purchase;
  }
}
