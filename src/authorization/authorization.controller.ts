import { Body, Controller, HttpCode, Param, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { CreateManualExclusionRequest } from 'src/models/authorization';
import { UserAuthorizationResponse } from 'src/models/authorization/access-control-list';
import { LoanAuthorizationRequest } from 'src/models/authorization/loan-authorization.request';
import { AuthorizationService } from './authorization.service';

@ApiTags('Authorization')
@Controller('authorization')
export class AuthorizationController {
  constructor(private authz: AuthorizationService) {}

  @Post('loan/:loanNumber')
  @HttpCode(200)
  public getAccessControl(
    @Param('loanNumber') loanNumber: string,
    @Body() authRequest: LoanAuthorizationRequest,
  ): Observable<UserAuthorizationResponse> {
    return this.authz.getUserAuthorizations(loanNumber, authRequest.pilotIds);
  }

  @Post('loan/:loanNumber/exclusion')
  public createExclusion(
    @Param('loanNumber') loanNumber: string,
    @Body() exclusionRequest: CreateManualExclusionRequest,
  ) {
    return this.authz.createExclusion(loanNumber, exclusionRequest);
  }

  @Post('loan/:loanNumber/access')
  @HttpCode(200)
  public getLoanAccess(
    @Param('loanNumber') loanNumber: string,
    @Body() authRequest: LoanAuthorizationRequest,
    @Query('apiVersion') apiVersion?: string,
  ) {
    const parsedApiVersion = apiVersion ? parseInt(apiVersion) : undefined;

    return this.authz.getLoanAccess(loanNumber, authRequest.pilotIds, parsedApiVersion);
  }

  @Post('loans/:loanNumber')
  @HttpCode(200)
  public getLoansAccess(
    @Param('loanNumber') loanNumber: string,
    @Body() authRequest: LoanAuthorizationRequest,
  ) {
    // This endpoint is used by the archived loan service
    // It returns the same data as getUserAuthorizations but with a different path
    return this.authz.getUserAuthorizations(loanNumber, authRequest.pilotIds);
  }
}
