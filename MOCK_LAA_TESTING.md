# Mock LAA Response Testing for Archived Loans

This document explains how to test the mock LAA responses for archived loans in local development.

## What Was Added

1. **Mock Response File**: `src/laa/mock-laa-responses.ts`
   - Contains mock responses for archived and non-archived loans
   - Defines which loan IDs should return archived responses
   - Includes helper function to determine if a loan should be archived

2. **Enhanced LAA Service**: `src/laa/laa.service.ts`
   - Added mock data functionality to `getV3UserAccess` method
   - Checks environment configuration to enable/disable mocking
   - Returns appropriate mock responses based on loan ID patterns

3. **New Authorization Endpoint**: `src/authorization/authorization.controller.ts`
   - Added `POST /authorization/loans/:loanNumber` endpoint
   - This matches the endpoint called by the archived loan service

4. **Environment Configuration**: `config/env/development.template.env`
   - Added `LAA_USE_MOCK_DATA=true` to enable mock responses

## How to Test

### 1. Enable Mock Data
Make sure your local environment file has:
```
LAA_USE_MOCK_DATA=true
```

### 2. Test with Archived Loan IDs
The following loan IDs will return archived responses:
- `123456789`
- `ARCHIVED001`
- `ARCHIVED002`
- `TEST-ARCHIVED`
- Any loan ID containing "archived" (case insensitive)
- Any loan ID ending with "999", "000", or "arch"

### 3. Test with Non-Archived Loan IDs
Any other loan ID will return a non-archived response.

### 4. API Testing
You can test the endpoint directly:

```bash
# Test archived loan
curl -X POST http://localhost:3000/authorization/loans/ARCHIVED001 \
  -H "Content-Type: application/json" \
  -d '{"pilotIds": ["LoanIsArchived", "rl-xp"]}'

# Test non-archived loan
curl -X POST http://localhost:3000/authorization/loans/NORMAL123 \
  -H "Content-Type: application/json" \
  -d '{"pilotIds": ["LoanIsArchived", "rl-xp"]}'
```

### 5. Expected Responses

**Archived Loan Response:**
```json
{
  "isUserAuthorized": true,
  "applicationAuth": {
    "LoanIsArchived": {
      "exclusion": {
        "reason": "LoanIsArchived",
        "message": "This loan is archived and cannot be edited"
      }
    },
    "rl-xp": {
      "exclusions": [
        {
          "reason": "InAmpArchive",
          "message": "Loan is in AMP archive"
        }
      ]
    }
  }
}
```

**Non-Archived Loan Response:**
```json
{
  "isUserAuthorized": true,
  "applicationAuth": {
    "LoanIsArchived": {},
    "rl-xp": {
      "exclusions": []
    }
  }
}
```

## Integration with UI

The UI's archived loan service should now receive these mock responses when running locally, allowing you to test the archived loan banner messages and form disabling functionality.

## Customizing Mock Data

To add more archived loan IDs or change the response format:

1. Edit `src/laa/mock-laa-responses.ts`
2. Add loan IDs to `mockArchivedLoanIds` array
3. Modify the `shouldReturnArchivedResponse` function for custom logic
4. Update the mock response objects as needed

## Debugging

The LAA service logs debug messages when using mock data. Check your console for:
- "Using mock LAA response for loan {loanId} with pilots: {pilotIds}"
- "Returning mock archived response for loan {loanId}"
- "Returning mock non-archived response for loan {loanId}"
