#!/usr/bin/env node

/**
 * Simple test script to verify mock LAA responses are working
 * Run with: node test-mock-laa.js
 */

const http = require('http');

const BFF_BASE_URL = 'http://localhost:3000';

function makeRequest(loanId, description) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      pilotIds: ['LoanIsArchived', 'rl-xp']
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/authorization/loans/${loanId}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ loanId, description, response, statusCode: res.statusCode });
        } catch (error) {
          reject({ loanId, description, error: 'Invalid JSON response', data });
        }
      });
    });

    req.on('error', (error) => {
      reject({ loanId, description, error: error.message });
    });

    req.write(postData);
    req.end();
  });
}

function isArchivedResponse(response) {
  const loanIsArchived = response?.applicationAuth?.LoanIsArchived?.exclusion?.reason;
  const rlXpExclusions = response?.applicationAuth?.['rl-xp']?.exclusions || [];
  const hasInAmpArchive = rlXpExclusions.some(ex => ex.reason === 'InAmpArchive');
  
  return !!loanIsArchived || hasInAmpArchive;
}

async function runTests() {
  console.log('🧪 Testing Mock LAA Responses\n');
  console.log('Make sure your BFF is running on localhost:3000 with LAA_USE_MOCK_DATA=true\n');

  const testCases = [
    { loanId: '1234567890', description: 'Explicit archived loan ID (10-digit)', expectedArchived: true },
    { loanId: '9999999999', description: '10-digit loan ID ending with "999"', expectedArchived: true },
    { loanId: '1000000000', description: '10-digit loan ID ending with "000"', expectedArchived: true },
    { loanId: '2000000999', description: 'Another 10-digit loan ID ending with "999"', expectedArchived: true },
    { loanId: '3000000000', description: 'Another 10-digit loan ID ending with "000"', expectedArchived: true },
    { loanId: '1234567891', description: 'Normal 10-digit loan ID', expectedArchived: false },
    { loanId: '5555555555', description: 'Another normal 10-digit loan ID', expectedArchived: false },
    { loanId: 'TEST-ARCHIVED', description: 'Non-numeric ID containing "archived" (fallback)', expectedArchived: true },
  ];

  let passed = 0;
  let failed = 0;

  for (const testCase of testCases) {
    try {
      const result = await makeRequest(testCase.loanId, testCase.description);
      
      if (result.statusCode !== 200) {
        console.log(`❌ ${testCase.description} (${testCase.loanId})`);
        console.log(`   Status: ${result.statusCode}`);
        console.log(`   Response: ${JSON.stringify(result.response, null, 2)}\n`);
        failed++;
        continue;
      }

      const isArchived = isArchivedResponse(result.response);
      const success = isArchived === testCase.expectedArchived;

      if (success) {
        console.log(`✅ ${testCase.description} (${testCase.loanId})`);
        console.log(`   Expected archived: ${testCase.expectedArchived}, Got: ${isArchived}\n`);
        passed++;
      } else {
        console.log(`❌ ${testCase.description} (${testCase.loanId})`);
        console.log(`   Expected archived: ${testCase.expectedArchived}, Got: ${isArchived}`);
        console.log(`   Response: ${JSON.stringify(result.response, null, 2)}\n`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${testCase.description} (${testCase.loanId})`);
      console.log(`   Error: ${error.error || error.message}`);
      if (error.data) {
        console.log(`   Raw response: ${error.data}`);
      }
      console.log('');
      failed++;
    }
  }

  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
  
  if (failed > 0) {
    console.log('\n💡 Troubleshooting:');
    console.log('   - Make sure the BFF is running on localhost:3000');
    console.log('   - Verify LAA_USE_MOCK_DATA=true in your environment');
    console.log('   - Check that the mock LAA service is properly configured');
    process.exit(1);
  } else {
    console.log('\n🎉 All tests passed! Mock LAA responses are working correctly.');
  }
}

runTests().catch(console.error);
